using LiteDB;
using LiteTest.Lite;
using System.Data;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Media;
using System.Text;
using System.Threading;
using System.Windows.Forms;
using SpeechLib1;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using System.IO.Ports;//���Ӱ�װ NUget System.IO.Ports
using Microsoft.Win32;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.ToolBar;
using System.Threading.Tasks;
using System.Diagnostics.Eventing.Reader;
using static System.Net.Mime.MediaTypeNames;
using System.Timers;
using System.Security.Cryptography;
using System.Xml.Linq;
using System.Linq;
using Weightdb;
using System.Reflection.Emit;
using System.Reflection;
using static System.ComponentModel.Design.ObjectSelectorEditor;
using System.Net.Http;





//���ڲ����ṹ��
struct COMPORT_ATTRIBUTE
{
    public int bandrate;
    public int data_bit;
    public Parity parity_check_bit;
    public StopBits stop_bit;
    public string comport_number;
};

namespace LiteTest
{
    public partial class Form1 : Form
    {
        // ���ڲ���
        private COMPORT_ATTRIBUTE uart_port;
        //ʵ����������
        public System.IO.Ports.SerialPort _serialPort = new System.IO.Ports.SerialPort();


        public Form1()
        {
           // JGtextBox = new System.Windows.Forms.TextBox();//�������Զ���д��¼��ʵ����
            InitializeComponent();
            LoadContentFromFile();//���ؼӹ����úͲ�������ʷ��¼
            InitializeSerialSet(); // ��ʼ����������
            lite = new("productdata.db");///////ע�����ݿ��ַ
            products = lite.GetCollection<ProductItem>("products");//������Ʒ��Ϣ��
            weights = lite.GetCollection<WeightItem>("weights");//�������ؼ�¼��
            System.Windows.Forms.Control.CheckForIllegalCrossThreadCalls = false;//�����̼߳��

        }
        readonly LiteDatabase lite;
        readonly ILiteCollection<ProductItem> products;//��Ʒ��Ϣ��
        readonly ILiteCollection<WeightItem> weights;//���ؼ�¼��
        bool isOpened = false;//����״̬��־
        bool isEabled = false;//������Ϣ����״̬��־
       // bool snOk = false;//��ʾ���������������룬�ſ��Դ������յ��ӳ�����
                              // bool allPass = false;//�����������PASS��־
                              //int countNG = 0;//ȫ�ֱ��������ڼ�¼ͬ�����������NG����
        int countOK;//ͳ���������
        double weight1, weight2, weight3;
        public int pageSize = 23;//��¼ҳ��ÿҳ22��
        public int recordCount = 0;//��¼������
        public int pageCount = 0;//��¼��ҳ��
        public int currentPage = 0;//��ǰҳ

        DataTable tablepage = new DataTable();//�����¼ҳ�ı�,f��ҳʹ��

        //��ʼ������
        public void InitializeSerialSet()
        {

            // ��ʼ��ɨ�贮��
            InitializePorts();
            // ��ʼ��������
            uart_port.bandrate = 9600;
            // ��ʼ������λ
            uart_port.data_bit = 8;
            // ��ʼ��ֹͣλ
            uart_port.stop_bit = (StopBits)1;
            // ��ʼ��У��λ
            uart_port.parity_check_bit = 0;//Parity.None


        }
        /// <summary>
        /// ɨ�贮��
        /// </summary>
        public void InitializePorts()
        {

            string[] port_names = SerialPort.GetPortNames();
            string last_name = "";

            COMComboBox.Items.Clear();//�������
            if (port_names == null)
            {
                MessageBox.Show("����û�д��ڣ�", "Error");
                return;
            }
            foreach (string s in System.IO.Ports.SerialPort.GetPortNames())
            {
                //��ȡ�ж��ٸ�COM�ھ����ӽ�COMBOX��Ŀ�б�  
                COMComboBox.Items.Add(s);
                last_name = s;//�������µ�һ��
            }
            COMComboBox.Text = last_name;//��ʾ���µ�һ������
            uart_port.comport_number = last_name;//��ֵ����

        }

        ///////////�л����뷨����
        private void SwitchToLanguageMode(string cultureType)
        {
            var installedInputLanguages = InputLanguage.InstalledInputLanguages;

            if (installedInputLanguages.Cast<InputLanguage>().Any(i => i.Culture.Name == cultureType))
            {
                InputLanguage.CurrentInputLanguage = InputLanguage.FromCulture(System.Globalization.CultureInfo.GetCultureInfo(cultureType));
            }
        }
        ///
        /// <summary>
        /// �򿪴���
        /// </summary>
        private bool OpenComport()
        {
            //�򿪴���
            if (COMComboBox.Items.Count > 0 && COMComboBox.Text != null)
            {
                if (_serialPort.IsOpen)
                {

                    //�رպ����´�
                    CloseComport();
                    if (_serialPort.IsOpen)//����ر�ʧ�ܾ��˳�
                    {

                        return false;
                    }

                }
                //��������
                _serialPort.PortName = COMComboBox.Text;            //��������
                _serialPort.BaudRate = uart_port.bandrate;      //������
                _serialPort.DataBits = uart_port.data_bit;      //����λ
                _serialPort.Parity = uart_port.parity_check_bit;//У��λ
                _serialPort.StopBits = uart_port.stop_bit;      //ֹͣλ

                try
                {
                    _serialPort.Open(); //�򿪴���
                    COMComboBox.Enabled = false;//��ʹ��
                    isOpened = true;
                    button1.Text = "�رմ���";//�����Ѿ��򿪣��滻��ť������ɫ��������ɫ
                    button1.BackColor = Color.MediumSeaGreen;//��ť������ɫ����
                    button1.ForeColor = Color.White;//��ť������ɫ����
                    ///�򿪴��ں����ز��ְ�ť����������
                    clearbox.Visible = false;//������հ�ť
                    �鿴ָ��.Visible = false;//����������ť
                    ����.Visible = false;//���ظ���������ť
                    ����̶�.Visible = false;//�������Ӱ�ť
                    button4.Visible = false;//�����޸ı��水ť
                    textsn.Focus();//��궨λ�����������
                    MessageBox.Show("���ڴ򿪳ɹ�", "�򿪴�����ʾ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                   // if(snOk)
                   // {
                        _serialPort.DataReceived += new SerialDataReceivedEventHandler(ReceiveData);//���ڽ��մ�������
                   // }
                  

                }
                catch
                {
                    MessageBox.Show("���ڴ�ʧ�ܣ�");

                    return false;
                }
                return true;
            }
            else
            {
                MessageBox.Show("���ӳӵĴ�����û�����ӵ���", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            // return true;
        }

        /// <summary>
        /// �رմ���
        /// </summary>
        private void CloseComport()
        {
            try
            {
                _serialPort.Close(); //�رմ���
                COMComboBox.Enabled = true;//��ʹ��
                isOpened = false;
                button1.Text = "�򿪴���";//���ڹر�
                button1.BackColor = Color.Transparent;//��ť������ɫ��͸��
                button1.ForeColor = Color.MediumPurple;//��ť������ɫ����ɫ
                //�رմ��ڿ�����ʾ��ť
                clearbox.Visible = true;//��ʾ��հ�ť
                �鿴ָ��.Visible = true;//����������ť
                ����.Visible = true;//���ظ���������ť
                ����̶�.Visible = true;//�������Ӱ�ť
                button4.Visible = true;//�����޸ı��水ť

                MessageBox.Show("�����������ر�", "�رմ�����ʾ", MessageBoxButtons.OK, MessageBoxIcon.Warning);

            }
            catch
            {
                MessageBox.Show("���ڹر�ʧ�ܣ�");
            }
        }

        /// <summary>
        /// ���ؽ����߳�
        /// </summary>

        /// ��������
        /// </summary>
        private void ReceiveData(object sender, System.IO.Ports.SerialDataReceivedEventArgs e)//���մ������ݣ�������UI
        {
            //�����߳�
          
                string str = _serialPort.ReadExisting();//�ַ�����ʽ��
                string receivedText = str.Trim();//ȥ�����˿հ��ַ�
                double doubleNumber;

                bool success = double.TryParse(str, out doubleNumber);//��׼����

                if (success)
                {
                    weight1 = doubleNumber;
                   
                }
                // else
                //  {
                //      MessageBox.Show("���ӳ�����ת������" + "\n\r" + " ����ɨ��", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                //     return;
                //  }

                //  weight1 = Convert.ToDouble(str);//ʵ������
                // weight2 = Convert.ToDouble(maxweightBox.Text);//����
                // weight3 = Convert.ToDouble(minweightBox.Text);//����

                this.BeginInvoke(new Action(delegate
                {
                    if (isEabled && success)//�����Ʒ��Ϣ�Ѿ��������ҵ��ӳ�����ת���ɹ�
                    {
                        if ((weight1 > weight3 && weight1 < weight2) || (weight1 == weight3) || (weight1 == weight2))//��������ڲ�ƷԤ���������֮�ڻ��ߵ��������ޣ����ж��ϸ񣬽�����ֵ��ʾ����
                        {
                            label2_weight.Text = "";//�������һ�ε�����
                            label2_weight.Text = str;//������ʾ����ֵ
                            label2_weight.ForeColor = Color.LimeGreen;
                            double errvalue1;//���ֵ
                            errvalue1 = Convert.ToDouble(label2_weight.Text) - Convert.ToDouble(weightBox.Text);//�������ֵ
                            string errvalue = String.Format("{0:F}", errvalue1);
                            lbpass.ForeColor = Color.LimeGreen;
                            lbpass.Text = "PASS";//��ʾpass
                            CVoice.Play("������ȷ");//��������
                            //snOk = false;//ȡ�����յ��ӳ�����
                            countOK++;//ͳ��ͨ���ϸ�����
                            passnum.Text = Convert.ToString(countOK);//�����������
                            lbwarning2.Text = textsn.Text;
                            lbwarning2.ForeColor = Color.LimeGreen;
                            label2_weight.ForeColor = Color.LimeGreen;

                            Newweight();//���������ϸ�ĳ��ؼ�¼�����ݿ�
                            listBox1.Items.Add(textsn.Text);//ֻ������pass������pass���Ű����뱣����listbox1������ͬһ���ε������ظ��ж�
                            listBox2.Items.Add(Convert.ToDouble(label2_weight.Text));//���������������ݣ�����ͳ��ƽ���������Сֵ
                                                                                     //�������ί�еķ�ʽ����߳̿������⣻
                                                                                     //this.BeginInvoke(new Action(delegate{  }));                                                     //����Ҫ�����еĵط�ֱ��copy���´��뼴�ɣ�
                                                                                     // this.Invoke(new Action(delegate
                                                                                     // {
                                                                                     //��datagridview����Winform DataGridView�ؼ���������ֱ�����ֹ���������濨������취

                            /*����Ϊ�������еĹ��ܴ��룬�ɱ��滻*/
                            DataGridViewRow dr = new DataGridViewRow();
                            dr.CreateCells(dataGridView1);
                            dataGridView1.Rows.Add(new string[] { Convert.ToString(countOK), modelBox.Text, textsn.Text, label2_weight.Text, errvalue, lbpass.Text, DateTime.Now.ToString() });//������ȷ����
                            dataGridView1.Sort(dataGridView1.Columns[6], ListSortDirection.Descending);//ʱ���н�������
                            dataGridView1.ClearSelection();//ȡ��Ĭ��ѡ����
                                                           //dataGridView1.Rows.Add("1", "2");
                            this.dataGridView1.Update();
                            /*����Ϊ�������еĹ��ܴ��룬�ɱ��滻*/

                            // }));
                            //׼����һ������
                            textsn.Text = "";//�Զ��������
                            textsn.Enabled = true;//������¼��
                            textsn.SelectAll();//ѡ����������
                            textsn.Focus();

                             //return; ;//�ϸ��˳�����
                        }
                        if (weight1 < weight3 || weight1 > weight2)//||weight1 == weight3||weight1 == weight2)//��������ڲ�ƷԤ���������֮�࣬���ж��ϸ񣬽�����ֵ��ʾ����
                        {
                            label2_weight.Text = "";//�������һ�ε�����
                            label2_weight.Text = str;//������ʾ����ֵ
                            label2_weight.ForeColor = Color.Red;
                            lbpass.ForeColor = Color.Red;
                            lbpass.Text = "NG";//��ʾpass
                            CVoice.Play("��������");//���Ŵ�������
                            //snOk = false;//ȡ�����յ��ӳ�����
                            lbwarning2.Text = "������������";
                            lbwarning2.ForeColor = Color.Red;
                            label2_weight.ForeColor = Color.Red;
                            textsn.Enabled = true;//������¼��
                            textsn.SelectAll();//ѡ����������
                            textsn.Focus();
                            //  return; ;//�˳�����

                        }
                      

                    }
                }));
            

        }



        private void button1_Click(object sender, EventArgs e)//�򿪴��ڰ�ť
        {

            if (!isOpened)
            {
                OpenComport();//�򿪴���
            }
            else
            {
                CloseComport();
            }
        }


        private void snBox_KeyPress(object sender, KeyPressEventArgs e)//SN�ı��������س����ж������Ƿ��ظ��������Ƿ���ȷ
        {
            if (!isEabled && snBox.Text != "" && e.KeyChar == 13)//���δ��������
            {
                String str = snBox.Text;
                int alpha = 0, digit = 0, blank = 0, num1 = 0, othernum = 0;//�����ַ�
                blank = str.Split(' ').Length - 1;//ͳ�ƿո�����
                foreach (char c in str)
                {
                    if (Char.IsLetter(c)) ++alpha;//ͳ����ĸ����
                    else if (Char.IsDigit(c)) ++digit;//ͳ����������
                    else ++othernum;//�����Ƿ��ַ�
                    num1 = alpha + digit + othernum;//���ַ���
                }
                if (alpha < 10 && blank == 0)//��ĸ����С��5����û�пո�û�зǷ��ַ�
                {
                    if (MessageBox.Show("����:" + snBox.Text + "\n\r" + "���ȣ�" + num1 + "λ", "ȷ��������", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
                    {
                        snnumBox.Text = Convert.ToString(num1);
                        snBox.Text = null;//��������
                        snBox.Focus();

                    }

                }
                else
                {
                    MessageBox.Show("�������пո��4��������ĸ��Ƿ��ַ�" + "\n\r" + " ����ɨ��", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    snBox.SelectAll();//ѡ����������
                    snBox.Focus();
                }
            }
        }
        private void textsn_KeyPress(object sender, KeyPressEventArgs e)//SN�ı��������س����ж������Ƿ��ظ��������Ƿ���ȷ
        {
            if (isEabled && isOpened)
            {
                if (textsn.Text == "" && e.KeyChar == 13)//�����Ѵ�
                {
                    lbpass.ForeColor = Color.Red;
                    lbpass.Text = "NG";//��ʾpass
                    CVoice.Play("û������");//���Ŵ�������
                    lbwarning2.Text = "û������";//
                    lbwarning2.ForeColor = Color.Red;
                    //textsn.Text = "";//�Զ��������
                    label2_weight.Text = "";
                    textsn.SelectAll();//ѡ������1����
                    textsn.Focus();
                    MessageBox.Show("û��������" + "\n\r" + " ����ɨ��", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return; ;//�˳��������ȵ��´δ���}
                }
                if (e.KeyChar == 13 && textsn.Text.Length != Convert.ToInt32(snnumBox.Text))//���볤�ȴ���
                {
                    lbpass.ForeColor = Color.Red;
                    lbpass.Text = "NG";//��ʾpass
                    CVoice.Play("�������");//���Ŵ�������
                    lbwarning2.Text = "���볤�ȴ���";//
                    lbwarning2.ForeColor = Color.Red;
                    //textsn.Text = "";//�Զ��������
                    label2_weight.Text = "";
                    textsn.SelectAll();//ѡ������1����
                    textsn.Focus();
                    MessageBox.Show("���볤�ȴ���" + "\n\r" + " ����ɨ��", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return; ;//�˳��������ȵ��´δ���}
                }
                if (textsn.Text != "" && e.KeyChar == 13 && textsn.Text.Length == Convert.ToInt32(snnumBox.Text))//�����Ѵ�
                {

                    //////////���ж���1�����Ǻ�����������Ŀǰ���������ֻ�ܳ���1����ĸ������Ϊ����
                    String str = textsn.Text;
                    String str2 = textsn.Text.Substring(0, ordernumBox.TextLength); ;//�������н�ȡ�����ų����ַ������������ڶԱȶ������Ƿ���ȷ
                    int alpha = 0, digit = 0, blank = 0, num1 = 0, othernum1 = 0, othernum2 = 0;
                    blank = str.Split(' ').Length - 1;//ͳ�ƿո�����
                    foreach (char c in str)
                    {
                        if (Char.IsLetter(c)) ++alpha;//ͳ����ĸ����
                        else if (Char.IsDigit(c)) ++digit;//ͳ����������
                                                          // else ++othernum1;//�����Ƿ��ַ�
                        else if (str.Contains("#")) ++othernum2;//�����Ƿ��ַ�
                        else ++othernum1;//�����Ƿ��ַ�
                        num1 = alpha + digit + othernum1 + othernum2;//���ַ���
                    }
                    if (alpha >= 10)//��������5����ĸ
                    {
                        lbpass.ForeColor = Color.Red;
                        lbpass.Text = "NG";//��ʾpass
                        CVoice.Play("�������");//���Ŵ�������
                        lbwarning2.Text = "��������5����ĸ";//
                        lbwarning2.ForeColor = Color.Red;
                        label2_weight.Text = "";//�������һ�ε�����
                        textsn.SelectAll();//ѡ������1����
                        textsn.Focus();
                        return; ;//�˳��������ȵ��´δ���
                    }
                    if (blank > 0)//�����пո�
                    {
                        lbpass.ForeColor = Color.Red;
                        lbpass.Text = "NG";//��ʾpass
                        CVoice.Play("�������");//���Ŵ�������
                        lbwarning2.Text = "�������пո�";//
                        lbwarning2.ForeColor = Color.Red;
                        label2_weight.Text = "";//�������һ�ε�����
                        textsn.SelectAll();//ѡ������1����
                        textsn.Focus();
                        return; ;//�˳��������ȵ��´δ���
                    }
                    if (num1 != Convert.ToInt32(snnumBox.Text))//���볤�Ȳ�����
                    {
                        lbpass.ForeColor = Color.Red;
                        lbpass.Text = "NG";//��ʾpass
                        CVoice.Play("�������");//���Ŵ�������
                        lbwarning2.Text = "�����ַ����ȴ���";//
                        lbwarning2.ForeColor = Color.Red;
                        label2_weight.Text = "";//�������һ�ε�����
                        textsn.SelectAll();//ѡ������1����
                        textsn.Focus();
                        return; ;//�˳��������ȵ��´δ���
                    }
                    if (othernum1 > 0)//�������зǷ��ַ�
                    {
                        lbpass.ForeColor = Color.Red;
                        lbpass.Text = "NG";//��ʾpass
                        CVoice.Play("�������");//���Ŵ�������
                        lbwarning2.Text = "�����зǷ��ַ�";//
                        lbwarning2.ForeColor = Color.Red;
                        label2_weight.Text = "";//�������һ�ε�����
                        textsn.SelectAll();//ѡ������1����
                        textsn.Focus();
                        return; ;//�˳��������ȵ��´δ���
                    }
                    if (othernum2 > 2)//�������зǷ��ַ�
                    {
                        lbpass.ForeColor = Color.Red;
                        lbpass.Text = "NG";//��ʾpass
                        CVoice.Play("�������");//���Ŵ�������
                        lbwarning2.Text = "�ж��#��";//
                        lbwarning2.ForeColor = Color.Red;
                        label2_weight.Text = "";//�������һ�ε�����
                        textsn.SelectAll();//ѡ������1����
                        textsn.Focus();
                        return; ;//�˳��������ȵ��´δ���
                    }
                    if (str2 != ordernumBox.Text)//�����Ŵ���
                    {
                        lbpass.ForeColor = Color.Red;
                        lbpass.Text = "NG";//��ʾpass
                        CVoice.Play("�������");//���Ŵ�������
                        lbwarning2.Text = "�����Ŵ���/�ͺŴ���";//
                        lbwarning2.ForeColor = Color.Red;
                        label2_weight.Text = "";//�������һ�ε�����
                        textsn.SelectAll();//ѡ������1����
                        textsn.Focus();
                        return; ;//�˳��������ȵ��´δ���
                    }

                    ///////////
                    if (listBox1.Items.Count > 0)//�ж������Ƿ��ظ�
                    {
                        for (int i = 0; i < listBox1.Items.Count; i++)
                        {
                            if (listBox1.Items[i].ToString() == textsn.Text)
                            {
                                lbpass.ForeColor = Color.Red;
                                lbpass.Text = "�ظ�";//��ʾpass
                                lbwarning2.Text = textsn.Text;
                                lbwarning2.ForeColor = Color.Red;
                                CVoice.Play("�����ظ�");//������ȷ����
                                label2_weight.Text = "";//�������һ�ε�����
                                textsn.SelectAll();//ѡ������1����
                                textsn.Focus();
                                return; ;
                            }
                        }
                    }
                    //snOk = true;//���Դ����ͽ��յ��ӳ�����
                   _serialPort.Write("RN");//����ϸ�����ָ��ȶ�����������Ҫһ��
                                            //System.Threading.Thread.Sleep(500);//�ӳ�
                }
            }
            else
            {
                if (e.KeyChar == 13)//�س�
                {
                    MessageBox.Show("1���ȴ򿪴���" + "\n\r" + "2�������ʼ���ذ�ť����ܳ���", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return; ;//�ϸ��˳�����
                }
            }


        }

        private void �鿴ָ��_Click(object sender, EventArgs e)//������ť��������Ʒ��Ϣ������䵽�ı���
        {
            if (productcodeBox.Enabled)//�����Ʒ������ǿ�����״̬���Ҳ�Ϊ�գ��Ž�������
            {
                if (!string.IsNullOrEmpty(productcodeBox.Text))//��Ʒ���벻Ϊ��
                {
                    var r1 = products.FindOne(x => x.Productcode == productcodeBox.Text);//��Ʒ����productcode
                    if (r1 != null)//�����Ʒ������ڣ�����Ϣ��ʾ����Ӧ�ı���
                    {

                        //  

                        productcodeBox.Text = r1.Productcode;//��Ʒ����
                        ordernumBox.Text = r1.Ordernum;//������
                        modelBox.Text = r1.Model;//��Ʒ�ͺ�
                        weightBox.Text = Convert.ToString(r1.Weight);//��׼��������������תΪ�ı���ʾ
                        maxweightBox.Text = Convert.ToString(r1.Maxweight);//�������ޣ���������תΪ�ı���ʾ
                        minweightBox.Text = Convert.ToString(r1.Minweight);//�������ޣ���������תΪ�ı���ʾ
                        snnumBox.Text = Convert.ToString(r1.Snnum);//���볤��
                        averagevalue.Text = weightBox.Text;//ͳ������ʼֵ��ƽ��ֵ
                        maxvalue.Text = maxweightBox.Text;//����ֵ
                        minvalue.Text = minweightBox.Text;//����ֵ
                        labeldate.Text = "�������ڣ�" + r1.Sndate;// DateTime.Now.ToLocalTime().ToString();
                    }
                    else
                    {
                        MessageBox.Show("û�иò�Ʒ�������Ʒ�����Ƿ���ȷ�������²�Ʒ������", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        productcodeBox.SelectAll();
                        productcodeBox.Focus();
                    }
                }
                else
                {
                    MessageBox.Show("������Ϊ�գ��������Ʒ���������", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    productcodeBox.Focus();
                    // CVoice.Play("OK");//��������
                }

            }
            else //��Ʒ��Ϣ����
            { MessageBox.Show("������Ϣ�������������������", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Warning); }


        }
        private void searchBox_KeyPress(object sender, KeyPressEventArgs e)//�س�������¼
        {
            //throw new NotImplementedException();
            if (e.KeyChar == 13)
            {

                searchweight();
                // return;
            }
        }
        private void �鿴ָ����һ��_Click(object sender, EventArgs e)//��¼ҳ������ť
        {
            //dataGridView3.Rows.Clear();

            searchweight();
            //return;
        }
        private void searchweight()//������¼����
        {
            if (string.IsNullOrEmpty(searchBox.Text))
            {
                MessageBox.Show("������Ϊ�գ��޷�����", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                // return; ;
            }
            if (!string.IsNullOrEmpty(searchBox.Text))//������Ϊ��
            {                                                           // var all = products.FindAll();
                                                                        //һ��Ϊ����ʱ�ľֲ�����  

                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("���");
                dataTable.Columns.Add("��Ʒ����");
                //dataTable.Columns.Add("������");
                dataTable.Columns.Add("��Ʒ�ͺ�");
                dataTable.Columns.Add("SN��");
                dataTable.Columns.Add("����");
                dataTable.Columns.Add("���");
                dataTable.Columns.Add("���");
                dataTable.Columns.Add("�ӹ�����");
                dataTable.Columns.Add("������");
                dataTable.Columns.Add("����");

                if (snradioButton.Checked)//�л�����ѯҲʱ��Ĭ������SN��
                {
                    // var r1 = weights.Find(x => x.Productsn == searchBox.Text);//��׼��ѯ
                    var r1 = weights.Find(Query.Contains("Productsn", searchBox.Text));//ģ����ѯ
                    foreach (var data in r1)
                    {
                        dataTable.Rows.Add(data.Id, data.Productcode, data.Model, data.Productsn, data.Weight,
                            data.Weighterr, data.Pass, data.Jgno, data.Scperson, data.Weightdate
                          );

                    }

                }
                if (ModelradioButton.Checked)//��ѯ��Ʒ�ͺ�
                {
                    // var r1 = weights.Find(x => x.Productsn == searchBox.Text);//
                    var r1 = weights.Find(Query.Contains("Model", searchBox.Text));//ģ����ѯ
                    foreach (var data in r1)
                    {
                        dataTable.Rows.Add(data.Id, data.Productcode, data.Model, data.Productsn, data.Weight,
                            data.Weighterr, data.Pass, data.Jgno, data.Scperson, data.Weightdate
                          );

                    }

                }
                if (jgradioButton.Checked)//�����ӹ�����
                {
                    // var r1 = weights.Find(x => x.Jgno == searchBox.Text);//
                    var r1 = weights.Find(Query.Contains("Jgno", searchBox.Text));//ģ����ѯ
                    foreach (var data in r1)
                    {
                        dataTable.Rows.Add(data.Id, data.Productcode, data.Model, data.Productsn, data.Weight,
                            data.Weighterr, data.Pass, data.Jgno, data.Scperson, data.Weightdate
                          );
                        // i++;
                    }

                }
                if (scradioButton.Checked)//��������Ա
                {
                    //var r1 = weights.Find(x => x.Scperson == '%'+searchBox.Text+ '%');//
                    var r1 = weights.Find(Query.Contains("Scperson", searchBox.Text));//ģ����ѯ
                    foreach (var data in r1)
                    {
                        dataTable.Rows.Add(data.Id, data.Productcode, data.Model, data.Productsn, data.Weight,
                            data.Weighterr, data.Pass, data.Jgno, data.Scperson, data.Weightdate
                          );

                    }

                }
                if (coderadioButton.Checked)//������Ʒ����
                {
                    // var r1 = weights.Find(x => x.Productcode == searchBox.Text);//
                    var r1 = weights.Find(Query.Contains("Productcode", searchBox.Text));//ģ����ѯ
                    foreach (var data in r1)
                    {
                        dataTable.Rows.Add(data.Id, data.Productcode, data.Model, data.Productsn, data.Weight,
                            data.Weighterr, data.Pass, data.Jgno, data.Scperson, data.Weightdate
                          );

                    }

                }

                ///������Դ
                recordCount = dataTable.Rows.Count;     //��¼������
                if (recordCount != 0)
                {
                    pageCount = (recordCount / pageSize);
                    if ((recordCount % pageSize) > 0)
                    {
                        pageCount++;
                    }

                    //Ĭ�ϵ�һҳ
                    currentPage = 1;
                    if (currentPage < 1) currentPage = 1;//��1ҳ
                    if (currentPage > pageCount) currentPage = pageCount;//���һҳ

                    int beginRecord;    //��ʼָ��
                    int endRecord;      //����ָ��
                    DataTable dtTemp;
                    dtTemp = dataTable.Clone();

                    beginRecord = pageSize * (currentPage - 1);//����
                    if (currentPage == 1) beginRecord = 0;
                    endRecord = pageSize * currentPage;//������

                    if (currentPage == pageCount) endRecord = recordCount;

                    for (int x = beginRecord; x < endRecord; x++)
                    {
                        dtTemp.ImportRow(dataTable.Rows[x]);
                    }

                    //dataGridView3.Rows.Clear();
                    labPageIndex.Text = "��" + currentPage.ToString() + "ҳ";//��ǰҳ
                    labRecordCount.Text = "��: " + recordCount.ToString() + " ����" + pageCount.ToString() + "ҳ";//�ܼ�¼��
                    dataGridView3.DataSource = dtTemp;

                }
                if (recordCount == 0)
                {
                    MessageBox.Show("û�м�¼" + "\n\r" + "�����������ݻ����ͷ���ȷ", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    searchBox.SelectAll();
                    searchBox.Focus();
                    // return; ;
                }
            }

        }


        private void ����_Click(object sender, EventArgs e)//�������
        {
            //����
            var tr = new ProductItem();
            products.Insert(tr);
            // var tr2 = new WeightItem();
            // weights.Insert(tr2);
            // tr.Productcode = DateTime.Now.ToString();//��Ʒ����productcode
            //    tr.Minweight = DateTime.Now.TimeOfDay.ToString();//��������minweight

        }

        private void ����̶�_Click(object sender, EventArgs e)//�����²�Ʒ��ť�������µĲ�Ʒ��Ϣ
        {
            //�����²�Ʒ�������벻����״̬�¾�������

            var r1 = products.FindOne(x => x.Productcode == productcodeBox.Text);//��Ʒ����productcode
                                                                                 // var r2 = weights.FindOne(x => x.Productcode == productcodeBox.Text);//��Ʒ����productcode
                                                                                 // var tr = new WeightItem();
                                                                                 // tr.Productcode = productcodeBox.Text;//��Ʒ����
                                                                                 // tr.Model = modelBox.Text;//��Ʒ�ͺ�
                                                                                 //  weights.Insert(tr);//���ӱ���
            if (r1 != null)//����ܴ����ݿ����������룬����ظ�����ֹ����
            {

                productcodeBox.SelectAll();//ѡ�в�Ʒ�����
                productcodeBox.Focus();//��������λ�ڱ����
                MessageBox.Show("��Ʒ�����Ѵ��ڣ������ظ����ӣ�����", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Error);


            }
            else //��Ʒ���벻�ظ�
            {
                List<System.Windows.Forms.TextBox> textBoxes = new List<System.Windows.Forms.TextBox>
                {
                productcodeBox, ordernumBox,modelBox, weightBox,maxweightBox, minweightBox,snnumBox  };// ��鼴������Ĳ�Ʒ��Ϣ�ı����Ƿ��пյ�

                if (textBoxes.Any(tb => string.IsNullOrEmpty(tb.Text)))
                {
                    MessageBox.Show("���飬������һ���ı���û����д", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
                else//�ı��򶼲�Ϊ�գ������Ʒ��Ϣ�����ݿ�
                {
                    if (JGtextBox.Text.Contains(" "))//����ı������Ƿ��пո��п��������뷨Ϊ�л����������쳣���ַ�֮���пո�
                    {
                        MessageBox.Show("�ӹ����ſ�---�д��ڿո�\n\nͬʱ��ctrl��+shift���л�ΪӢ�����뷨��������������", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                    if (SCtextBox.Text.Contains(" "))
                    {
                        MessageBox.Show("����Ա��---�д��ڿո�\n\nͬʱ��ctrl��+shift���л�ΪӢ�����뷨��������������", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                    if (productcodeBox.Text.Contains(" "))
                    {
                        MessageBox.Show("��Ʒ�����---�д��ڿո�\n\nͬʱ��ctrl��+shift���л�ΪӢ�����뷨��������������", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                    if (ordernumBox.Text.Contains(" "))
                    {
                        MessageBox.Show("�����ſ�---�д��ڿո�\n\nͬʱ��ctrl��+shift���л�ΪӢ�����뷨��������������", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                    if (modelBox.Text.Contains(" "))
                    {
                        MessageBox.Show("��Ʒ�ͺſ�---�д��ڿո�\n\nͬʱ��ctrl��+shift���л�ΪӢ�����뷨��������������", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                    if (weightBox.Text.Contains(" "))
                    {
                        MessageBox.Show("��׼������---�д��ڿո�\n\nͬʱ��ctrl��+shift���л�ΪӢ�����뷨��������������", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                    if (maxweightBox.Text.Contains(" "))
                    {
                        MessageBox.Show("�������޿�---�д��ڿո�\n\nͬʱ��ctrl��+shift���л�ΪӢ�����뷨��������������", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                    if (minweightBox.Text.Contains(" "))
                    {
                        MessageBox.Show("�������޿�---�д��ڿո�\n\nͬʱ��ctrl��+shift���л�ΪӢ�����뷨��������������", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }
                    if (snnumBox.Text.Contains(" "))
                    {
                        MessageBox.Show("SN���ȿ�---�д��ڿո�\n\nͬʱ��ctrl��+shift���л�ΪӢ�����뷨��������������", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    if (MessageBox.Show("��Ʒ���룺" + productcodeBox.Text + "\n\r" + "�����ţ�" + ordernumBox.Text + "\n\r" + "��Ʒ�ͺţ�" + modelBox.Text
                       + "\n\r" + "��׼������" + weightBox.Text + "\n\r" + "���������" + maxweightBox.Text
                       + "\n\r" + "��С������" + minweightBox.Text + "\n\r" + "SN���ȣ�" + snnumBox.Text,
                       "��ʾ��ȷ�����������Ʒ��", MessageBoxButtons.OKCancel, MessageBoxIcon.Question) == DialogResult.OK)
                    {
                        Newproduct();//
                        textsn.Focus();//��궨λ�����������
                                       // MessageBox.Show("�²�Ʒ���ӳɹ�", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }

                }
            }


        }
        private void Newproduct()//�����²�Ʒ
        {
            var tr = new ProductItem();
            tr.Productcode = productcodeBox.Text;//��Ʒ����
            tr.Ordernum = ordernumBox.Text;//������
            tr.Model = modelBox.Text;//��Ʒ�ͺ�
            tr.Weight = Convert.ToDouble(weightBox.Text);//��׼���������ı�תΪ���������ֱ���
            tr.Maxweight = Convert.ToDouble(maxweightBox.Text);//������������ı�תΪ���������ֱ���
            tr.Minweight = Convert.ToDouble(minweightBox.Text);//��С���������ı�תΪ���������ֱ���
            tr.Snnum = Convert.ToInt32(snnumBox.Text);//���кų���
            tr.Sndate = DateTime.Now.ToLocalTime().ToString();//������Ʒʱ��
            products.Insert(tr);//���ӱ���
            MessageBox.Show("�²�Ʒ���ӳɹ�", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Information); // 
        }
        private void Newweight()//���Ӻϸ�ĳ��ؼ�¼
        {
            var tr = new WeightItem();
            tr.Productcode = productcodeBox.Text;//��Ʒ����
            tr.Model = modelBox.Text;//��Ʒ�ͺ�
            tr.Productsn = textsn.Text;//��Ʒ���к�
            tr.Weight = Convert.ToDouble(label2_weight.Text);//ʵ������
            string errvalue = String.Format("{0:F}", Convert.ToDouble(label2_weight.Text) - Convert.ToDouble(weightBox.Text));//����2λС��
            tr.Weighterr = Convert.ToDouble(errvalue);//�������
            tr.Pass = lbpass.Text;//���ؽ��
            tr.Jgno = JGtextBox.Text;//�ӹ�����
            tr.Scperson = SCtextBox.Text;//������
            tr.Weightdate = DateTime.Now.ToLocalTime().ToString();//������Ʒʱ��
            weights.Insert(tr);//���ӱ���
            //MessageBox.Show("�²�Ʒ���ӳɹ�", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Information); // 
        }
        private void Commit_Click(object sender, EventArgs e)
        {
            lite.Commit();
        }

        private void ����_Click(object sender, EventArgs e)//����������ť
        {
            if (productcodeBox.Text != null && averagevalue.Text != "0.00")//��Ʒ�����ͳ������Ϊ��
            {
                var r1 = products.FindOne(x => x.Productcode == productcodeBox.Text);//��Ʒ����productcode
                                                                                     // r1.Minweight = "44423";//��������minweight
                if (r1 != null)//���ҵ���Ӧ�Ĳ�Ʒ
                {

                    if (MessageBox.Show("����ǰ��" + "                   ���º�" + "\n\r" +
                        "��׼������" + weightBox.Text + "��   ��׼������" + averagevalue.Text + "\n\r" +
                        "�������ޣ�" + maxweightBox.Text + "�� �������ޣ�" + maxvalue.Text + "\n\r" +
                       "�������ޣ�" + minweightBox.Text + "��   �������ޣ�" + minvalue.Text,
                           "��ʾ��ȷ�����²�Ʒ������Ϣ��", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) == DialogResult.OK)
                    {
                        r1.Weight = Convert.ToDouble(averagevalue.Text);//ƽ��ֵ
                        r1.Maxweight = Convert.ToDouble(maxvalue.Text);//���ֵ
                        r1.Minweight = Convert.ToDouble(minvalue.Text);//��Сֵ
                        r1.Sndate = DateTime.Now.ToLocalTime().ToString();//����ʱ��
                        products.Update(r1);//���µ����ݿ�
                        if (!weightBox.Enabled)//���������Ƚ��������޸ģ��ڻָ������ı���
                        {
                            weightBox.Enabled = true;//��׼����
                            weightBox.Text = averagevalue.Text;//ƽ��ֵ�����½�������
                            weightBox.Enabled = false;//��׼����
                            maxweightBox.Enabled = true;//��������
                            maxweightBox.Text = maxvalue.Text;
                            maxweightBox.Enabled = false;//��������
                            minweightBox.Enabled = true;//��������
                            minweightBox.Text = minvalue.Text;
                            minweightBox.Enabled = false;//��������
                        }
                        else//û��������ֱ���޸�
                        {

                            weightBox.Text = averagevalue.Text;//ƽ��ֵ�����½�������
                            maxweightBox.Text = maxvalue.Text;//����
                            minweightBox.Text = minvalue.Text;//����

                        }
                        MessageBox.Show("������Ϣ���³ɹ�", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    MessageBox.Show("û�иò�Ʒ�������Ʒ�����Ƿ���ȷ�������²�Ʒ������", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    productcodeBox.SelectAll();
                    productcodeBox.Focus();
                }
            }
            else
            {
                MessageBox.Show("��Ʒ�����ͳ����Ϊ0.00������", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                productcodeBox.Focus();
            }


        }

        private void ɾ��ȫ��_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("��պ����ݲ��ɻָ���ȷ�������",
                       "��ʾ��������������ݿ�", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) == DialogResult.OK)
            {
                if (MessageBox.Show("��պ����ݲ��ɻָ���ȷ�������",
                       "��ʾ��������������ݿ�", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) == DialogResult.OK)
                {
                    products.DeleteAll();
                    MessageBox.Show("ɾ���ɹ�", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }

        private void ɾ��ָ��_Click(object sender, EventArgs e)
        {
            products.Delete(1);
        }

        private void ɾ��ָ����һ��_Click(object sender, EventArgs e)//ɾ����Ʒ
        {
            if (this.dataGridView2.SelectionMode != DataGridViewSelectionMode.FullColumnSelect)
            {
                int index = dataGridView2.CurrentRow.Index; //��ȡѡ���е��к�
                DataGridViewRow row = dataGridView2.Rows[index];
                string str = dataGridView2.Rows[index].Cells[1].Value.ToString() ?? "ѡ����д���";//ɾ��ѡ���е��ͺ�
                if (MessageBox.Show("ȷ��ɾ������Ĳ�Ʒ��" + "\n\r" +
                    "��Ʒ���룺" + str + "\n\r" + "��Ʒ�ͺţ�" + dataGridView2.Rows[index].Cells[3].Value.ToString(),
                          "��ʾ��������ɾ����Ʒ", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) == DialogResult.OK)
                {
                    var r1 = products.FindOne(x => x.Productcode == str);//ɾ����Ʒ�����Ӧ�Ĳ�Ʒ����Ʒ������Ψһ��
                    products.Delete(r1.Id);//�����ݿ�ɾ����Ʒ
                    dataGridView2.Rows.Remove(row);//ɾ��������ʾ�ı�����
                    MessageBox.Show("��Ʒɾ���ɹ�", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
        }



        private void COMComboBox_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void Form1_Load(object sender, EventArgs e)//���ش���ʱ�����ز�Ʒ�ͼ�¼����
        {
            dataGridView1.ClearSelection();//ȡ��Ĭ��ѡ����
            DisplayPresetData();//���صڶ�ҳ����
            PageSorter();//���ص�3ҳȫ��

            //JGtextBox.Text = Properties.Settings.Default.jgdh ?? "";//��һ�εļӹ�����
            //SCtextBox.Text = Properties.Settings.Default.czr ?? "";//��һ�εĲ�����
            //dataGridView1.CurrentCell = null;
            //dataGridView1.Rows[0].Selected = false;

        }

        private void button2_Click(object sender, EventArgs e)//�ֶ������ȡ������ť
        {
            _serialPort.Write("RN");//��ȡ��������
        }
        private void LoadContentFromFile()//�������Զ���д��ʷ��¼
        {
            string filePath = @"加工单号和操作人员历史.txt"; // �滻Ϊʵ��·�����ļ���

            if (File.Exists(filePath))
            {
                string[] lines = File.ReadAllLines(filePath);
                if (lines.Length >= 2)
                {
                    JGtextBox.Text = lines[0];
                    SCtextBox.Text = lines[1];
                }
            }
        }

        private void button3_Click(object sender, EventArgs e)//������ť��������Ϣ,������ع����б䶯
        {
            SwitchToLanguageMode("en-US");//ǿ�����뷨�л���Ӣ��״̬
            string filePath = @"加工单号和操作人员历史.txt"; // �滻Ϊʵ��·�����ļ���


            if (!isEabled)//���û������
            {
                // List<System.Windows.Forms.TextBox> textBoxes = new List<System.Windows.Forms.TextBox>
                //  {JGtextBox,
                //      SCtextBox,
                //  productcodeBox, ordernumBox,modelBox, weightBox,maxweightBox, minweightBox,snnumBox };// ��鼴������Ĳ�Ʒ��Ϣ�ı����Ƿ��пյ�

                bool hasEmptyTextBox = string.IsNullOrEmpty(JGtextBox.Text) || string.IsNullOrEmpty(SCtextBox.Text) || string.IsNullOrEmpty(productcodeBox.Text) ||
                  string.IsNullOrEmpty(ordernumBox.Text) || string.IsNullOrEmpty(modelBox.Text) || string.IsNullOrEmpty(weightBox.Text) || string.IsNullOrEmpty(maxweightBox.Text) ||
                 string.IsNullOrEmpty(minweightBox.Text) || string.IsNullOrEmpty(snnumBox.Text);


                if (hasEmptyTextBox)//textBoxes.Any(tb => string.IsNullOrEmpty(tb.Text)))
                {
                    MessageBox.Show("���飬������һ���ı���û����д", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return; ;//�˳���������Ҫ���µ����ť����
                }
                else//�ı��򶼲�Ϊ�գ���ֹ¼�롣����״̬��ʾ��ʼɨ�����
                {


                    string content1 = JGtextBox.Text;
                    string content2 = SCtextBox.Text;
                    File.WriteAllLines(filePath, new string[] { content1, content2 });//����Ϊ��ʷ��¼
                    double doubleNumber1, doubleNumber2;
                    bool success1 = double.TryParse(maxweightBox.Text, out doubleNumber1);//��������
                    bool success2 = double.TryParse(minweightBox.Text, out doubleNumber2);//��������
                    if (success1 && success2)
                    {
                        weight2 = doubleNumber1;//��������
                        weight3 = doubleNumber2;//��������
                    }
                    else
                    {
                        MessageBox.Show("�������޻�����δ�ɹ�ת��Ϊ����", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    JGtextBox.Enabled = false;//�ӹ�����
                    SCtextBox.Enabled = false;//������
                    snBox.Enabled = false;//��ȡSN���ȵ��ı���
                    productcodeBox.Enabled = false;//��Ʒ����
                    ordernumBox.Enabled = false;//������
                    modelBox.Enabled = false;//��Ʒ�ͺ�
                    weightBox.Enabled = false;//��׼����
                    maxweightBox.Enabled = false;//��������
                    minweightBox.Enabled = false;//��������
                    snnumBox.Enabled = false;//���볤��

                    isEabled = true;//�Ƿ������ı�־��true������falseû��������������

                    clearbox.Visible = false;//������հ�ť
                    �鿴ָ��.Visible = false;//����������������ť����ֹ����ȫ���޸Ĳ���
                    ����.Visible = false;//���������ظ���������ť
                    ����̶�.Visible = false;//�������������Ӱ�ť
                    button4.Visible = false;//�����������޸ı��水ť

                    button3.Text = "��������";
                    button3.BackColor = Color.MediumSeaGreen;//��ť������ɫ
                    button3.ForeColor = Color.White;//��ť������ɫ����ɫ
                                                    // openserial();/////////////////////////////////////////////////////�򿪴���
                    textsn.SelectAll();
                    textsn.Focus();//��궨λ�����������


                }
            }
            else
            {

                JGtextBox.Enabled = true;//�ӹ�����
                SCtextBox.Enabled = true;//������
                snBox.Enabled = true;//�����Ʒ��Ϣ�ı���������޸�����
                productcodeBox.Enabled = true;//�������
                ordernumBox.Enabled = true;
                modelBox.Enabled = true;
                weightBox.Enabled = true;
                maxweightBox.Enabled = true;
                minweightBox.Enabled = true;
                snnumBox.Enabled = true;
                isEabled = false;//��������
                textsn.Enabled = true;//��1����򲻿�¼��
                button3.Text = "��ʼ����";
                button3.BackColor = Color.Transparent;//��ť������ɫ��͸��
                button3.ForeColor = Color.MediumPurple;//��ť������ɫ����ɫ

                if (isOpened)
                {
                    CloseComport();//�رմ���
                }
                if (!isOpened)
                {
                    //�رմ��ں���ʾ��ť
                    clearbox.Visible = true;//��ʾ��հ�ť
                    �鿴ָ��.Visible = true;//��ʾ������ť
                    ����.Visible = true;//��ʾ����������ť
                    ����̶�.Visible = true;//��ʾ���Ӱ�ť
                    button4.Visible = true;//��ʾ�޸ı��水ť
                }

                //new һ������Ϊ listBox2.Items.Count������
                if (listBox2.Items.Count > 0)//�кϸ���������ݣ��������Զ�������ʾ
                {
                    double[] a = new double[listBox2.Items.Count];//��listbox2�е�����������ȡ������
                                                                  //ѭ������listBox2�е�ÿһ��
                    for (int i = 0; i < listBox2.Items.Count; i++)
                    {
                        //��ֵ������

                        a[i] = Convert.ToDouble(listBox2.Items[i]);
                        //MessageBox.Show(a[i].ToString());
                    }

                    //averagevalue.Text = String.Format("{0:F}", Convert.ToString(a.Average()));//ƽ��ֵ����2λС��
                    averagevalue.Text = a.Average().ToString("F2");//ƽ��ֵ����2λС��
                    maxvalue.Text = Convert.ToString(a.Max());//���ֵ
                    minvalue.Text = Convert.ToString(a.Min());//��Сֵ
                    labeldate.Text = "������£�" + DateTime.Now.ToLocalTime().ToString();//����ͳ�Ƹ���ʱ��
                    this.listBox2.Items.Clear();
                    //��յ�ǰ�ͺŵ������б���ʷ������û���˳���������£������������ͺţ����¼����ƽ����������Сֵ����

                }
                // closeserial();//////////////////////////////////////////////////�رմ���
            }
        }

        private void snBox_LostFocus(object sender, EventArgs e)//SNʾ���ı�����ʾ��
        {
            if (snBox.Text == "")
            {
                snBox.Text = "ɨ��ǹɨSN��ȡ�ַ���";
                snBox.ForeColor = Color.Gray;
            }

        }
        private void snBox_GotFocus(object sender, EventArgs e)//SNʾ���ı�����ʾ��
        {
            if (snBox.Text == "ɨ��ǹ����SN��ȡ�ַ���")
            {
                snBox.Text = "";
                snBox.ForeColor = Color.Black;
            }

        }

        private void �鿴ȫ��_Click(object sender, EventArgs e)//�鿴ȫ����Ʒ���ݰ�ť
        {
            DisplayPresetData();
            textBox1.Clear();//�������
        }
        private DataTable GetAll()//��ȡȫ����Ʒ��Ϣ
        {
            int i = 0;
            var all = products.FindAll();
            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("���");
            dataTable.Columns.Add("��Ʒ����");
            dataTable.Columns.Add("������");
            dataTable.Columns.Add("��Ʒ�ͺ�");
            dataTable.Columns.Add("��׼����(g)");
            dataTable.Columns.Add("��������(g)");
            dataTable.Columns.Add("��������(g)");
            dataTable.Columns.Add("���볤��");
            dataTable.Columns.Add("��������");

            foreach (var data in all)
            {
                dataTable.Rows.Add(data.Id, data.Productcode, data.Ordernum, data.Model,
                   data.Weight, data.Maxweight, data.Minweight, data.Snnum, data.Sndate);
                i++;
            }

            label18.Text = "����" + i + "  ����Ʒ";//��ѯ����Ϊ��
            return dataTable;
        }
        /// ��ҳ�ķ���
        /// </summary>
        /// <param name="str"></param>
        private void PageSorter()///////��¼ҳ��ҳ��ʾ����
        {

            var all = weights.FindAll();//��ѯ���ݿ��¼����Ϣ
            tablepage.Columns.Add("���");
            tablepage.Columns.Add("��Ʒ����");
            tablepage.Columns.Add("��Ʒ�ͺ�");
            tablepage.Columns.Add("SN��");
            tablepage.Columns.Add("����");
            tablepage.Columns.Add("���");
            tablepage.Columns.Add("���");
            tablepage.Columns.Add("�ӹ�����");
            tablepage.Columns.Add("������");
            tablepage.Columns.Add("����");

            foreach (var data in all)
            {
                tablepage.Rows.Add(data.Id, data.Productcode, data.Model, data.Productsn, data.Weight,
                    data.Weighterr, data.Pass, data.Jgno, data.Scperson, data.Weightdate
                  );
            }
            recordCount = tablepage.Rows.Count;     //��¼������
            pageCount = (recordCount / pageSize);
            if ((recordCount % pageSize) > 0)
            {
                pageCount++;
            }
            //Ĭ�ϵ�һҳ
            currentPage = 1;
            LoadPage();//���ü������ݵķ���
        }

        /// <summary>
        /// LoadPage����
        /// </summary>
        private void LoadPage()//����ҳ
        {
            if (currentPage < 1) currentPage = 1;//��1ҳ
            if (currentPage > pageCount) currentPage = pageCount;//���һҳ

            int beginRecord;    //��ʼָ��
            int endRecord;      //����ָ��
            DataTable dtTemp;
            dtTemp = tablepage.Clone();

            beginRecord = pageSize * (currentPage - 1);//��ʼ��
            if (currentPage == 1) beginRecord = 0;
            endRecord = pageSize * currentPage;//������

            if (currentPage == pageCount) endRecord = recordCount;

            for (int i = beginRecord; i < endRecord; i++)
            {
                dtTemp.ImportRow(tablepage.Rows[i]);
            }

            //dataGridView3.Rows.Clear();
            labPageIndex.Text = "��" + currentPage.ToString() + "ҳ";//��ǰҳ
            labRecordCount.Text = "��: " + recordCount.ToString() + " ���� " + pageCount.ToString() + "ҳ";//�ܼ�¼��
            dataGridView3.DataSource = dtTemp;
        }

        private void GetWeight()//��ȡȫ����¼
        {

            var all = weights.FindAll();
            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("���");
            dataTable.Columns.Add("��Ʒ����");
            dataTable.Columns.Add("��Ʒ�ͺ�");
            dataTable.Columns.Add("SN��");
            dataTable.Columns.Add("����");
            dataTable.Columns.Add("���");
            dataTable.Columns.Add("���");
            dataTable.Columns.Add("�ӹ�����");
            dataTable.Columns.Add("������");
            dataTable.Columns.Add("����");


            foreach (var data in all)
            {
                dataTable.Rows.Add(data.Id, data.Productcode, data.Model, data.Productsn, data.Weight,
                    data.Weighterr, data.Pass, data.Jgno, data.Scperson, data.Weightdate
                  );

            }
            ///������Դ
            recordCount = dataTable.Rows.Count;     //��¼������
            if (recordCount != 0)
            {
                pageCount = (recordCount / pageSize);
                if ((recordCount % pageSize) > 0)
                {
                    pageCount++;
                }

                //Ĭ�ϵ�һҳ
                currentPage = 1;
                if (currentPage < 1) currentPage = 1;//��1ҳ
                if (currentPage > pageCount) currentPage = pageCount;//���һҳ

                int beginRecord;    //��ʼָ��
                int endRecord;      //����ָ��
                DataTable dtTemp;
                dtTemp = dataTable.Clone();

                beginRecord = pageSize * (currentPage - 1);//��ʼ��
                if (currentPage == 1) beginRecord = 0;
                endRecord = pageSize * currentPage;//������

                if (currentPage == pageCount) endRecord = recordCount;

                for (int x = beginRecord; x < endRecord; x++)
                {
                    dtTemp.ImportRow(dataTable.Rows[x]);
                }

                //dataGridView3.Rows.Clear();
                labPageIndex.Text = "��" + currentPage.ToString() + "ҳ";//��ǰҳ
                labRecordCount.Text = "��: " + recordCount.ToString() + " ���� " + pageCount.ToString() + "ҳ";//�ܼ�¼��
                dataGridView3.DataSource = dtTemp;


            }


        }
        public void DisplayPresetData()//��ʾȫ����Ʒ����
        {
            dataGridView2.DataSource = GetAll();
        }

        private void tabControl1_SelectedIndexChanged(object sender, EventArgs e)//�л�ѡ���ʾ����
        {
            if (tabControl1.SelectedTab == tabPage2)//����л�����Ʒ��Ϣҳ��ˢ�²�Ʒ��Ϣ
            {
                // DisplayPresetData();//ִ����Ӧ�Ĳ�������������ʾ����
                textBox1.Focus();//��궨λ�������򣬲����μ��ز�Ʒ��Ϣ����Ҫ�ֶ����ȫ����ť�ֶ�����
            }
            if (tabControl1.SelectedTab == tabPage3)//�л�����ѯ��¼ҳ
            {

                snradioButton.Checked = true;//�л�����ѯ��¼ҳ��Ĭ��ѡ������SN
                searchBox.Focus();//���
                                  //�л�page������ȫ����¼���ֶ����ȫ����ť����
                                  // dataGridView3.DataSource = GetWeight();//��������ʾ����������
                                  // PageSorter();//�л�����¼ҳ����������ʷ��¼�����ⴰ�忨��

            }

        }

        private void dataGridView2_CellDoubleClick(object sender, EventArgs e)//˫��ѡ���Ʒ�У�������䵽�ı���
        {
            if (!isEabled) //û�������������
            {
                if (this.dataGridView2.SelectionMode != DataGridViewSelectionMode.FullColumnSelect)
                {
                    int index = dataGridView2.CurrentRow.Index; //��ȡѡ���е��к�
                                                                //snBox.Text= dataGridView2.Rows[index].Cells[0].Value.ToString();//0�������
                    tabControl1.SelectedTab = tabPage1;//˫����ص�����ҳ
                    productcodeBox.Text = dataGridView2.Rows[index].Cells[1].Value.ToString();
                    ordernumBox.Text = dataGridView2.Rows[index].Cells[2].Value.ToString();
                    modelBox.Text = dataGridView2.Rows[index].Cells[3].Value.ToString();
                    weightBox.Text = dataGridView2.Rows[index].Cells[4].Value.ToString();
                    maxweightBox.Text = dataGridView2.Rows[index].Cells[5].Value.ToString();
                    minweightBox.Text = dataGridView2.Rows[index].Cells[6].Value.ToString();
                    snnumBox.Text = dataGridView2.Rows[index].Cells[7].Value.ToString();


                };
            }
            else { MessageBox.Show("����ҳ��Ʒ�������ȷ��ؽ�����ѡ��", "����", MessageBoxButtons.OK, MessageBoxIcon.Error); }
        }

        private void button4_Click(object sender, EventArgs e)//�����޸�
        {
            List<System.Windows.Forms.TextBox> textBoxes = new List<System.Windows.Forms.TextBox>
                {
                productcodeBox, ordernumBox,modelBox, weightBox,maxweightBox, minweightBox,snnumBox  };// ��鼴������Ĳ�Ʒ��Ϣ�ı����Ƿ��пյ�

            if (textBoxes.Any(tb => string.IsNullOrEmpty(tb.Text)))
            {
                MessageBox.Show("���飬������һ���ı���û����д", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                //return; ;//�˳���������Ҫ���µ����ť����
            }
            else//�ı��򶼲�Ϊ�գ���ֹ¼�롣����״̬��ʾ��ʼɨ�����
            {
                weight1 = 0.00;
                weight2 = 0.00;
                weight3 = 0.00;
                var r1 = products.FindOne(x => x.Productcode == productcodeBox.Text);//��Ʒ����productcode
                if (r1 != null)//���ҵ���Ӧ�Ĳ�Ʒ
                {

                    if (MessageBox.Show("ȷ�������޸ĺ�Ĳ�Ʒ��Ϣ��",
                           "��ʾ�����ڱ����޸ĺ�Ĳ�Ʒ", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) == DialogResult.OK)
                    {
                        r1.Productcode = productcodeBox.Text;//��Ʒ����
                        r1.Ordernum = ordernumBox.Text;//������
                        r1.Model = modelBox.Text;//��Ʒ�ͺ�
                        r1.Weight = Convert.ToDouble(weightBox.Text);//��׼���������ı�תΪ���������ֱ���
                        r1.Maxweight = Convert.ToDouble(maxweightBox.Text);//������������ı�תΪ���������ֱ���
                        r1.Minweight = Convert.ToDouble(minweightBox.Text);//��С���������ı�תΪ���������ֱ���
                        r1.Snnum = Convert.ToInt32(snnumBox.Text);//���кų���
                        r1.Sndate = DateTime.Now.ToLocalTime().ToString();//�������ʱ��
                        products.Update(r1);//���µ����ݿ�
                        textsn.Focus();//��궨λ�����������
                        MessageBox.Show("�޸ı���ɹ�", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }
                else
                {
                    if (MessageBox.Show("�޸ĺ���һ���²�Ʒ��ȷ��������",
                        "��ʾ���Ƿ񱣴�ò�Ʒ", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) == DialogResult.OK)
                    {
                        Newproduct();
                        textsn.Focus();//��궨λ�����������
                        //MessageBox.Show("����ɹ�", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                }//�޸ĺ�Ĳ�Ʒ���²�Ʒ
            }
            // weight1 = Convert.ToDouble(weightBox.Text);
            //weight2 = Convert.ToDouble(maxweightBox.Text);
            // weight3 = Convert.ToDouble(minweightBox.Text);
        }

        private void lbwarning2_Click(object sender, EventArgs e)
        {

        }
        private void Form1_FormClosing(object sender, FormClosingEventArgs e)//���������ʾ
        {
            if (MessageBox.Show("ȷ��Ҫ�رմ�����", "�ر�ȷ��", MessageBoxButtons.YesNo) == DialogResult.Yes)
            {
                //�ر�ʱ�Զ�����
                string sourceFilePath1 = @"D:\\��װ��������\\productdata.db"; // �滻Ϊʵ�ʵ�Դ�ļ�·�����ļ���
                string sourceFilePath2 = @"D:\\��װ��������\\productdata-log.db"; // �滻Ϊʵ�ʵ�Դ�ļ�·�����ļ���
                string backupFolderPath = @"E:\\�������ݱ�����ɾ��\\�������ݱ���"; // �滻Ϊʵ�ʵı����ļ���·��

                if (File.Exists(sourceFilePath1) && File.Exists(sourceFilePath2))
                {
                    string fileName1 = Path.GetFileName(sourceFilePath1);
                    string fileName2 = Path.GetFileName(sourceFilePath2);
                    string datePart = DateTime.Now.ToString("yyyy��MM��dd��HHʱmm��ss��");
                    string backupFileName1 = $"{Path.GetFileNameWithoutExtension(fileName1)}_{datePart}{Path.GetExtension(fileName1)}";
                    string backupFileName2 = $"{Path.GetFileNameWithoutExtension(fileName2)}_{datePart}{Path.GetExtension(fileName2)}";
                    string destinationPath1 = Path.Combine(backupFolderPath, backupFileName1);
                    string destinationPath2 = Path.Combine(backupFolderPath, backupFileName2);

                    File.Copy(sourceFilePath1, destinationPath1, true);
                    File.Copy(sourceFilePath2, destinationPath2, true);
                    //MessageBox.Show("�ļ����ݳɹ�");
                }

                e.Cancel = false;//ȷ���ر�
                                 // COMComboBox.Items.Clear();//�˳��������������Ĵ��ں�
            }
            else
            {
                e.Cancel = true;//ȡ���ر�
            }
        }
        //����ر�
        private void Form1_FormClosed(object sender, FormClosedEventArgs e)//ǿ���˳��߳�
        {



            System.Windows.Forms.Application.Exit();
            // COMComboBox.Items.Clear();//�˳��������������Ĵ��ں�
            //ǿ�ƽ������̲��˳�
            //System.Diagnostics.Process.GetCurrentProcess().Kill();
            System.Environment.Exit(0);
        }

        private void clearbox_Click(object sender, EventArgs e)//��ղ�Ʒ��ť
        {
            if (MessageBox.Show("ȷ��Ҫ��յ�ǰ��Ʒ��",
                        "��ʾ", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) == DialogResult.OK)
            {
                // SCtextBox.Clear();//����ղ�����Ա
                // JGtextBox.Clear();//����ռӹ���
                productcodeBox.Clear();
                ordernumBox.Clear();
                modelBox.Clear();
                weightBox.Clear();
                maxweightBox.Clear();
                minweightBox.Clear();
                snnumBox.Clear();
                productcodeBox.Focus();//��궨λ
            }
        }

        private void comboBox1_SelectedIndexChanged(object sender, EventArgs e)
        {

        }

        private void button7_Click(object sender, EventArgs e)//ɾ����¼��ť
        {

            // weights.DeleteAll();
            if (this.dataGridView3.SelectionMode != DataGridViewSelectionMode.FullColumnSelect)
            {
                int index = dataGridView3.CurrentRow.Index; //��ȡѡ���е��к�
                DataGridViewRow row = dataGridView3.Rows[index];
                //////////SN��Ϊ��������ѯɾ��
                string str = dataGridView3.Rows[index].Cells[3].Value.ToString() ?? "ѡ����д���";//ɾ��ѡ���е����к�
                var r1 = weights.FindOne(x => x.Productsn == str);//�������к�
                /////////////////������������SNΪ�ձ����棬�޷�����ɾ��ʱ
                string str2 = dataGridView3.Rows[index].Cells[0].Value.ToString() ?? "ѡ����д���";//ɾ��ѡ������Ŷ�Ӧ�У��������SNΪ��ֵ�޷�ɾ��ʱ
                var r2 = weights.FindOne(x => x.Id == Convert.ToInt32(str2));//������¼��Ψһ���
                if (r1 != null)
                {
                    if (MessageBox.Show("ɾ����¼��" + "\n\r" +
                        "��ƷSN�ţ�" + str + "\n\r" + "��Ʒ�ͺţ�" + dataGridView3.Rows[index].Cells[2].Value.ToString(),
                              "��ʾ��������ɾ����¼", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) == DialogResult.OK)
                    {

                        weights.Delete(r1.Id);//�����ݿ�ɾ����Ʒ
                        dataGridView3.Rows.Remove(row);//ɾ��������ʾ�ı�����
                        MessageBox.Show("��¼ɾ���ɹ�", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        //return;
                    }
                }
                else
                {
                    if (MessageBox.Show("ɾ����¼��" + "\n\r" +
                       "��¼��ţ�" + dataGridView3.Rows[index].Cells[0].Value.ToString() + "\n\r" + "��Ʒ�ͺţ�" + dataGridView3.Rows[index].Cells[2].Value.ToString(),
                             "��ʾ��������ɾ����¼", MessageBoxButtons.OKCancel, MessageBoxIcon.Warning) == DialogResult.OK)
                    {
                        weights.Delete(r2.Id);//�����ݿ�ɾ����Ʒ
                        dataGridView3.Rows.Remove(row);//ɾ��������ʾ�ı�����
                        MessageBox.Show("��¼ɾ���ɹ�", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        // return;
                    }
                }
            }

        }

        private void button6_Click(object sender, EventArgs e)//��ռ�¼��ť
        {
            weights.DeleteAll();
            dataGridView3.Rows.Clear();


        }

        private void button5_Click(object sender, EventArgs e)//�鿴ȫ����¼��ť
        {
            GetWeight();

        }

        private void searchBox_TextChanged(object sender, EventArgs e)
        {

        }

        private void button8_Click(object sender, EventArgs e)//������¼��ť
        {
            using (SaveFileDialog saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.Filter = "CSV files (*.csv)|*.csv";//ֻ����CSV
                saveFileDialog.Title = "Save as";
                saveFileDialog.FileName = "���ؼ�¼-��" + currentPage.ToString() + "ҳ-" + $"{DateTime.Now:yyyy��MM��dd��HHʱmm��ss��}"; // Ĭ���ļ������Ե�ǰʱ������

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string filePath = saveFileDialog.FileName;
                    string fileExtension = Path.GetExtension(filePath).ToLower();

                    if (fileExtension == ".csv")
                    {
                        ExportDataToExcel(dataGridView3, filePath);
                        MessageBox.Show("�����ɹ�", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    // else if (fileExtension == ".xls")
                    // {
                    //  ExportDataToExcel(dataGridView3, filePath);
                    // }
                    else
                    {
                        MessageBox.Show("��֧�ֵ��ļ���ʽ.", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        /// <summary>
        ///  dataGridView ����Excel���ܺ���
        /// </summary>
        /// <param name="dataView">dataGridView���ݱ�</param>
        /// <param name="filePath">·��</param>
        private void ExportDataToExcel(DataGridView dataView, string filePath)
        {
            try
            {
                using (StreamWriter writer = new StreamWriter(filePath, false, Encoding.UTF8))
                {
                    // д���ͷ
                    for (int i = 0; i < dataView.Columns.Count; i++)
                    {
                        writer.Write(dataView.Columns[i].HeaderText);
                        if (i < dataView.Columns.Count - 1)
                        {
                            writer.Write(",");
                        }
                    }
                    writer.WriteLine();

                    // д������
                    foreach (DataGridViewRow row in dataView.Rows)
                    {
                        if (!row.IsNewRow) // ��������
                        {
                            for (int i = 0; i < dataView.Columns.Count; i++)
                            {
                                if (i == 1 || i == 3)//��Ʒ�����SN����Ϊ�ı�����
                                {
                                    writer.Write("'" + row.Cells[i].Value?.ToString());//��������תΪ�ı���ʽ���������ָ�ʽ��ʧ0���¿�ѧ����
                                }
                                else
                                { writer.Write(row.Cells[i].Value?.ToString()); }//
                                if (i < dataView.Columns.Count - 1)
                                {
                                    writer.Write(",");
                                }
                            }
                            writer.WriteLine();
                        }
                    }
                }

                MessageBox.Show("��¼�����ɹ�", "�ɹ�", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show("Error exporting data: " + ex.Message, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }



        private void label16_Click(object sender, EventArgs e)
        {

        }

        private void button11_Click(object sender, EventArgs e)//��¼ҳ����ҳ��ť
        {
            if (currentPage == 1)
            { return; }
            currentPage = 1;
            LoadPage();
        }

        private void button9_Click(object sender, EventArgs e)//��¼ҳ����һҳ��ť
        {
            if (currentPage == 1)
            { return; }
            currentPage--;
            LoadPage();
        }

        private void button10_Click(object sender, EventArgs e)//��¼ҳ����һҳ��ť
        {
            if (currentPage == pageCount)
            { return; }
            currentPage++;
            LoadPage();
        }

        private void button12_Click(object sender, EventArgs e)//��¼ҳ�����һҳ��ť
        {
            if (currentPage == pageCount)
            { return; }
            currentPage = pageCount;
            LoadPage();
        }

        private void tabPage3_Click(object sender, EventArgs e)
        {

        }
        private void searchModel()
        {
            if (string.IsNullOrEmpty(textBox1.Text))
            {
                MessageBox.Show("������Ϊ�գ��޷�����", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                // return; ;
            }
            if (!string.IsNullOrEmpty(textBox1.Text))//������Ϊ��
            {
                DataTable dataTable = new DataTable();
                dataTable.Columns.Add("���");
                dataTable.Columns.Add("��Ʒ����");
                dataTable.Columns.Add("������");
                dataTable.Columns.Add("��Ʒ�ͺ�");
                dataTable.Columns.Add("��׼����(g)");
                dataTable.Columns.Add("��������(g)");
                dataTable.Columns.Add("��������(g)");
                dataTable.Columns.Add("���볤��");
                dataTable.Columns.Add("��������");
                var r1 = products.Find(Query.Contains("Model", textBox1.Text));//�ڲ�Ʒ�ͺ�����ģ����ѯ
                foreach (var data in r1)
                {
                    dataTable.Rows.Add(data.Id, data.Productcode, data.Ordernum, data.Model,
                       data.Weight, data.Maxweight, data.Minweight, data.Snnum, data.Sndate);
                }
                if (dataTable.Rows.Count > 0)
                {
                    dataGridView2.DataSource = dataTable;
                    label18.Text = "����" + dataTable.Rows.Count + "  ����Ʒ";//��ѯ����Ϊ��
                    //MessageBox.Show("û�н��11" + "\n\r" + "�����������ݻ����ͷ���ȷ", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    //  return; ;
                }
                else
                {
                    MessageBox.Show("û�н��" + "\n\r" + "�����������ݻ����ͷ���ȷ", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    textBox1.SelectAll();
                    textBox1.Focus();
                    //return; ;
                }
            }
        }
        private void button13_Click(object sender, EventArgs e)//������Ʒ�ͺţ���ť
        {
            searchModel();//����������Ʒ�ͺź���
        }
        private void TextBox1_KeyPress(object sender, KeyPressEventArgs e)//�س�������Ʒ�ͺ�
        {
            // throw new NotImplementedException();
            if (e.KeyChar == 13)
            {
                searchModel();//����������Ʒ�ͺź���
                return;
            }
        }
        private void textBox1_LostFocus(object sender, EventArgs e)//������Ʒ��ʾ
        {
            if (textBox1.Text == "")
            {
                textBox1.Text = "������Ʒ�ͺ�";
                textBox1.ForeColor = Color.Gray;
            }


        }
        private void textBox1_GotFocus(object sender, EventArgs e)//������Ʒ��ʾ
        {
            if (textBox1.Text == "������Ʒ�ͺ�")
            {
                textBox1.Text = "";
                textBox1.ForeColor = Color.Black;
            }

        }
        private void searchBox_LostFocus(object sender, EventArgs e)//������¼��ʾ
        {
            if (searchBox.Text == "")
            {
                searchBox.Text = "ѡ�����ͣ�������������";
                searchBox.ForeColor = Color.Gray;
            }

        }
        private void searchBox_GotFocus(object sender, EventArgs e)//������¼��ʾ
        {
            if (searchBox.Text == "ѡ�����ͣ�������������")
            {
                searchBox.Text = "";
                searchBox.ForeColor = Color.Black;
            }

        }

        private void copyButton_Click(object sender, EventArgs e)//�������ݰ�ť�����һ�κ󣬽�D������ͬʱ�ֱ𱸷�2�ݵ�E�̵�2���ļ�����
        {
            string sourceFilePath1 = @"D:\\��װ��������\\productdata.db"; // �滻Ϊʵ�ʵ�Դ�ļ�·�����ļ���
            string sourceFilePath2 = @"D:\\��װ��������\\productdata-log.db"; // �滻Ϊʵ�ʵ�Դ�ļ�·�����ļ���
            string backupFolderPath = @"E:\\�������ݱ�����ɾ��\\�������ݱ���"; // �滻Ϊʵ�ʵı����ļ���·��

            if (File.Exists(sourceFilePath1) && File.Exists(sourceFilePath2))
            {
                string fileName1 = Path.GetFileName(sourceFilePath1);
                string fileName2 = Path.GetFileName(sourceFilePath2);
                string datePart = DateTime.Now.ToString("yyyy��MM��dd��HHʱmm��ss��");
                string backupFileName1 = $"{Path.GetFileNameWithoutExtension(fileName1)}_{datePart}{Path.GetExtension(fileName1)}";
                string backupFileName2 = $"{Path.GetFileNameWithoutExtension(fileName2)}_{datePart}{Path.GetExtension(fileName2)}";
                string destinationPath1 = Path.Combine(backupFolderPath, backupFileName1);
                string destinationPath2 = Path.Combine(backupFolderPath, backupFileName2);

                File.Copy(sourceFilePath1, destinationPath1, true);
                File.Copy(sourceFilePath2, destinationPath2, true);
                MessageBox.Show("�ļ����ݳɹ�");
            }
            else
            {
                MessageBox.Show("productdata��productdata-log�ļ���ʧ" + "\n\r" + "�������ϱ�", "������ʾ", MessageBoxButtons.OK, MessageBoxIcon.Warning);


            }
        }

        private void button14_Click(object sender, EventArgs e)//����ȫ����ť
        {
            var all = weights.FindAll();
            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("���");
            dataTable.Columns.Add("��Ʒ����");
            dataTable.Columns.Add("��Ʒ�ͺ�");
            dataTable.Columns.Add("SN��");
            dataTable.Columns.Add("����");
            dataTable.Columns.Add("���");
            dataTable.Columns.Add("���");
            dataTable.Columns.Add("�ӹ�����");
            dataTable.Columns.Add("������");
            dataTable.Columns.Add("����");


            foreach (var data in all)
            {
                dataTable.Rows.Add(data.Id, data.Productcode, data.Model, data.Productsn, data.Weight,
                    data.Weighterr, data.Pass, data.Jgno, data.Scperson, data.Weightdate
                  );

            }
            using (SaveFileDialog saveFileDialog = new SaveFileDialog())
            {
                saveFileDialog.Filter = "CSV files (*.csv)|*.csv";//ֻ����CSV
                saveFileDialog.Title = "Save as";
                saveFileDialog.FileName = "ȫ�����ؼ�¼" + $"{DateTime.Now:yyyy��MM��dd��HHʱmm��ss��}"; // Ĭ���ļ������Ե�ǰʱ������

                if (saveFileDialog.ShowDialog() == DialogResult.OK)
                {
                    string filePath = saveFileDialog.FileName;
                    string fileExtension = Path.GetExtension(filePath).ToLower();

                    if (fileExtension == ".csv")
                    {
                        ExportDataToExcel2(dataTable, filePath);
                        MessageBox.Show("�����ɹ�", "��ʾ", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    // else if (fileExtension == ".xls")
                    // {
                    //  ExportDataToExcel(dataGridView3, filePath);
                    // }
                    else
                    {
                        MessageBox.Show("��֧�ֵ��ļ���ʽ.", "����", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }
        private void ExportDataToExcel2(DataTable dataTable, string filePath)//ֱ��ȫ����������
        {
            using (StreamWriter writer = new StreamWriter(filePath, false, Encoding.UTF8))
            {
                // д������
                for (int i = 0; i < dataTable.Columns.Count; i++)
                {
                    writer.Write(dataTable.Columns[i].ColumnName);
                    if (i < dataTable.Columns.Count - 1)
                        writer.Write(",");
                }
                writer.WriteLine();

                // д��������
                foreach (DataRow row in dataTable.Rows)
                {
                   
                        for (int i = 0; i < dataTable.Columns.Count; i++)
                        {
                            if (i == 1 || i == 3)//��Ʒ�����SN����Ϊ�ı�����
                            {
                                writer.Write("'" + row[i].ToString());//��������תΪ�ı���ʽ���������ָ�ʽ��ʧ0���¿�ѧ����
                             }
                            else
                            {
                                writer.Write(row[i].ToString());
                            }
                            if (i < dataTable.Columns.Count - 1)
                            { 
                            writer.Write(",");
                             }
                           
                        }
                        writer.WriteLine();
                   
                }
            }
        }
    }
}
