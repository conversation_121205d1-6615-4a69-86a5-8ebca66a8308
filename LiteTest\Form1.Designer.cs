﻿namespace LiteTest
{
  partial class Form1
  {
    /// <summary>
    ///  Required designer variable.
    /// </summary>
    private System.ComponentModel.IContainer components = null;

    /// <summary>
    ///  Clean up any resources being used.
    /// </summary>
    /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
    protected override void Dispose(bool disposing)
    {
      if (disposing && (components != null))
      {
        components.Dispose();
      }
      base.Dispose(disposing);
    }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            // TextBox JGtextBox;
            JGtextBox = new TextBox();
            DataGridViewCellStyle dataGridViewCellStyle1 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle2 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle3 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle4 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle5 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle6 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle7 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle8 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle9 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle11 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle12 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle13 = new DataGridViewCellStyle();
            DataGridViewCellStyle dataGridViewCellStyle10 = new DataGridViewCellStyle();
            productItemBindingSource = new BindingSource(components);
            listBox2 = new ListBox();
            listBox1 = new ListBox();
            tabPage3 = new TabPage();
            button14 = new Button();
            button12 = new Button();
            button11 = new Button();
            labRecordCount = new Label();
            labPageIndex = new Label();
            button10 = new Button();
            button9 = new Button();
            ModelradioButton = new RadioButton();
            button8 = new Button();
            button5 = new Button();
            button7 = new Button();
            coderadioButton = new RadioButton();
            scradioButton = new RadioButton();
            jgradioButton = new RadioButton();
            snradioButton = new RadioButton();
            button6 = new Button();
            searchBox = new TextBox();
            dataGridView3 = new DataGridView();
            查看指定第一个 = new Button();
            tabPage2 = new TabPage();
            button13 = new Button();
            textBox1 = new TextBox();
            label18 = new Label();
            删除指定第一个 = new Button();
            查看全部 = new Button();
            dataGridView2 = new DataGridView();
            tabPage1 = new TabPage();
            passnum = new Label();
            label17 = new Label();
            label3 = new Label();
            groupBox2 = new GroupBox();
            copyButton = new Button();
            labeldate = new Label();
            minvalue = new Label();
            maxvalue = new Label();
            averagevalue = new Label();
            label4 = new Label();
            label6 = new Label();
            更新 = new Button();
            label5 = new Label();
            groupBox1 = new GroupBox();
            button3 = new Button();
            插入固定 = new Button();
            SCtextBox = new TextBox();
            snnumBox = new TextBox();
            label2 = new Label();
            button4 = new Button();
            label15 = new Label();
            snBox = new TextBox();
            button1 = new Button();
            clearbox = new Button();
            label14 = new Label();
            minweightBox = new TextBox();
            maxweightBox = new TextBox();
            weightBox = new TextBox();
            modelBox = new TextBox();
            ordernumBox = new TextBox();
            productcodeBox = new TextBox();
            label13 = new Label();
            label12 = new Label();
            label11 = new Label();
            label10 = new Label();
            查看指定 = new Button();
            label9 = new Label();
            label8 = new Label();
            label7 = new Label();
            COMComboBox = new ComboBox();
            dataGridView1 = new DataGridView();
            Column1 = new DataGridViewTextBoxColumn();
            Column2 = new DataGridViewTextBoxColumn();
            Column3 = new DataGridViewTextBoxColumn();
            Column4 = new DataGridViewTextBoxColumn();
            Column5 = new DataGridViewTextBoxColumn();
            Column6 = new DataGridViewTextBoxColumn();
            Column7 = new DataGridViewTextBoxColumn();
            label2_weight = new Label();
            lbwarning2 = new Label();
            textsn = new TextBox();
            label1 = new Label();
            lbpass = new Label();
            button2 = new Button();
            删除指定 = new Button();
            Commit = new Button();
            插入 = new Button();
            删除全部 = new Button();
            tabControl1 = new TabControl();
            JGtextBox = new TextBox();
            ((System.ComponentModel.ISupportInitialize)productItemBindingSource).BeginInit();
            tabPage3.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView3).BeginInit();
            tabPage2.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView2).BeginInit();
            tabPage1.SuspendLayout();
            groupBox2.SuspendLayout();
            groupBox1.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).BeginInit();
            tabControl1.SuspendLayout();
            SuspendLayout();
            // 
            // JGtextBox
            // 
            JGtextBox.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            JGtextBox.ForeColor = Color.Black;
            JGtextBox.Location = new Point(15, 100);
            JGtextBox.Name = "JGtextBox";
            JGtextBox.Size = new Size(438, 30);
            JGtextBox.TabIndex = 40;
            // 
            // productItemBindingSource
            // 
            productItemBindingSource.DataSource = typeof(Lite.ProductItem);
            // 
            // listBox2
            // 
            listBox2.FormattingEnabled = true;
            listBox2.Location = new Point(876, 35);
            listBox2.Name = "listBox2";
            listBox2.Size = new Size(33, 144);
            listBox2.TabIndex = 38;
            listBox2.Visible = false;
            // 
            // listBox1
            // 
            listBox1.FormattingEnabled = true;
            listBox1.Location = new Point(832, 35);
            listBox1.Name = "listBox1";
            listBox1.Size = new Size(33, 144);
            listBox1.TabIndex = 23;
            listBox1.Visible = false;
            // 
            // tabPage3
            // 
            tabPage3.Controls.Add(button14);
            tabPage3.Controls.Add(button12);
            tabPage3.Controls.Add(button11);
            tabPage3.Controls.Add(labRecordCount);
            tabPage3.Controls.Add(labPageIndex);
            tabPage3.Controls.Add(button10);
            tabPage3.Controls.Add(button9);
            tabPage3.Controls.Add(ModelradioButton);
            tabPage3.Controls.Add(button8);
            tabPage3.Controls.Add(button5);
            tabPage3.Controls.Add(button7);
            tabPage3.Controls.Add(coderadioButton);
            tabPage3.Controls.Add(scradioButton);
            tabPage3.Controls.Add(jgradioButton);
            tabPage3.Controls.Add(snradioButton);
            tabPage3.Controls.Add(button6);
            tabPage3.Controls.Add(searchBox);
            tabPage3.Controls.Add(dataGridView3);
            tabPage3.Controls.Add(查看指定第一个);
            tabPage3.Location = new Point(4, 29);
            tabPage3.Name = "tabPage3";
            tabPage3.Padding = new Padding(3);
            tabPage3.Size = new Size(1467, 818);
            tabPage3.TabIndex = 2;
            tabPage3.Text = "记录查询";
            tabPage3.UseVisualStyleBackColor = true;
            tabPage3.Click += tabPage3_Click;
            // 
            // button14
            // 
            button14.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            button14.Location = new Point(1199, 39);
            button14.Name = "button14";
            button14.Size = new Size(110, 40);
            button14.TabIndex = 26;
            button14.Text = "导出全部";
            button14.UseVisualStyleBackColor = true;
            button14.Click += button14_Click;
            // 
            // button12
            // 
            button12.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            button12.Location = new Point(771, 62);
            button12.Name = "button12";
            button12.Size = new Size(98, 40);
            button12.TabIndex = 25;
            button12.Text = "尾   页";
            button12.UseVisualStyleBackColor = true;
            button12.Click += button12_Click;
            // 
            // button11
            // 
            button11.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            button11.Location = new Point(771, 12);
            button11.Name = "button11";
            button11.Size = new Size(98, 40);
            button11.TabIndex = 24;
            button11.Text = "首   页";
            button11.UseVisualStyleBackColor = true;
            button11.Click += button11_Click;
            // 
            // labRecordCount
            // 
            labRecordCount.AutoSize = true;
            labRecordCount.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
            labRecordCount.ForeColor = Color.MediumPurple;
            labRecordCount.Location = new Point(524, 15);
            labRecordCount.Name = "labRecordCount";
            labRecordCount.Size = new Size(32, 27);
            labRecordCount.TabIndex = 21;
            labRecordCount.Text = "共";
            // 
            // labPageIndex
            // 
            labPageIndex.AutoSize = true;
            labPageIndex.Font = new Font("微软雅黑", 13.8F, FontStyle.Bold, GraphicsUnit.Point, 134);
            labPageIndex.ForeColor = Color.MediumSeaGreen;
            labPageIndex.Location = new Point(521, 43);
            labPageIndex.Name = "labPageIndex";
            labPageIndex.RightToLeft = RightToLeft.No;
            labPageIndex.Size = new Size(74, 31);
            labPageIndex.TabIndex = 20;
            labPageIndex.Text = "第1页";
            labPageIndex.TextAlign = ContentAlignment.MiddleCenter;
            // 
            // button10
            // 
            button10.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            button10.Location = new Point(914, 62);
            button10.Name = "button10";
            button10.Size = new Size(98, 40);
            button10.TabIndex = 23;
            button10.Text = "下一页";
            button10.UseVisualStyleBackColor = true;
            button10.Click += button10_Click;
            // 
            // button9
            // 
            button9.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            button9.Location = new Point(914, 13);
            button9.Name = "button9";
            button9.Size = new Size(98, 40);
            button9.TabIndex = 22;
            button9.Text = "上一页";
            button9.UseVisualStyleBackColor = true;
            button9.Click += button9_Click;
            // 
            // ModelradioButton
            // 
            ModelradioButton.AutoSize = true;
            ModelradioButton.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            ModelradioButton.Location = new Point(158, 50);
            ModelradioButton.Name = "ModelradioButton";
            ModelradioButton.Size = new Size(103, 28);
            ModelradioButton.TabIndex = 19;
            ModelradioButton.TabStop = true;
            ModelradioButton.Text = "产品型号";
            ModelradioButton.UseVisualStyleBackColor = true;
            // 
            // button8
            // 
            button8.Font = new Font("Microsoft YaHei UI", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            button8.Location = new Point(1054, 62);
            button8.Name = "button8";
            button8.Size = new Size(110, 40);
            button8.TabIndex = 18;
            button8.Text = "导出当前页";
            button8.UseVisualStyleBackColor = true;
            button8.Click += button8_Click;
            // 
            // button5
            // 
            button5.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            button5.Location = new Point(1054, 15);
            button5.Name = "button5";
            button5.Size = new Size(110, 40);
            button5.TabIndex = 16;
            button5.Text = "查看全部";
            button5.UseVisualStyleBackColor = true;
            button5.Click += button5_Click;
            // 
            // button7
            // 
            button7.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            button7.Location = new Point(1351, 38);
            button7.Name = "button7";
            button7.Size = new Size(110, 40);
            button7.TabIndex = 15;
            button7.Text = "删除";
            button7.UseVisualStyleBackColor = true;
            button7.Click += button7_Click;
            // 
            // coderadioButton
            // 
            coderadioButton.AutoSize = true;
            coderadioButton.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            coderadioButton.Location = new Point(10, 78);
            coderadioButton.Name = "coderadioButton";
            coderadioButton.Size = new Size(103, 28);
            coderadioButton.TabIndex = 14;
            coderadioButton.TabStop = true;
            coderadioButton.Text = "产品编码";
            coderadioButton.UseVisualStyleBackColor = true;
            // 
            // scradioButton
            // 
            scradioButton.AutoSize = true;
            scradioButton.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            scradioButton.Location = new Point(300, 50);
            scradioButton.Name = "scradioButton";
            scradioButton.Size = new Size(85, 28);
            scradioButton.TabIndex = 13;
            scradioButton.TabStop = true;
            scradioButton.Text = "操作人";
            scradioButton.UseVisualStyleBackColor = true;
            // 
            // jgradioButton
            // 
            jgradioButton.AutoSize = true;
            jgradioButton.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            jgradioButton.Location = new Point(158, 78);
            jgradioButton.Name = "jgradioButton";
            jgradioButton.Size = new Size(103, 28);
            jgradioButton.TabIndex = 12;
            jgradioButton.TabStop = true;
            jgradioButton.Text = "加工单号";
            jgradioButton.UseVisualStyleBackColor = true;
            // 
            // snradioButton
            // 
            snradioButton.AutoSize = true;
            snradioButton.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            snradioButton.Location = new Point(10, 50);
            snradioButton.Name = "snradioButton";
            snradioButton.Size = new Size(92, 28);
            snradioButton.TabIndex = 11;
            snradioButton.TabStop = true;
            snradioButton.Text = "产品SN";
            snradioButton.UseVisualStyleBackColor = true;
            // 
            // button6
            // 
            button6.Location = new Point(1398, 68);
            button6.Name = "button6";
            button6.Size = new Size(63, 31);
            button6.TabIndex = 6;
            button6.Text = "清空";
            button6.UseVisualStyleBackColor = true;
            button6.Visible = false;
            button6.Click += button6_Click;
            // 
            // searchBox
            // 
            searchBox.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point, 134);
            searchBox.Location = new Point(6, 11);
            searchBox.Name = "searchBox";
            searchBox.Size = new Size(379, 33);
            searchBox.TabIndex = 5;
            searchBox.TextChanged += searchBox_TextChanged;
            searchBox.GotFocus += searchBox_GotFocus;
            searchBox.KeyPress += searchBox_KeyPress;
            searchBox.LostFocus += searchBox_LostFocus;
            // 
            // dataGridView3
            // 
            dataGridView3.AllowUserToDeleteRows = false;
            dataGridViewCellStyle1.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            dataGridView3.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle1;
            dataGridView3.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells;
            dataGridView3.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            dataGridViewCellStyle2.BackColor = SystemColors.Control;
            dataGridViewCellStyle2.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle2.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle2.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle2.SelectionForeColor = SystemColors.HighlightText;
            dataGridView3.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle2;
            dataGridView3.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView3.Location = new Point(6, 112);
            dataGridView3.MultiSelect = false;
            dataGridView3.Name = "dataGridView3";
            dataGridView3.RowHeadersWidth = 51;
            dataGridViewCellStyle3.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            dataGridView3.RowsDefaultCellStyle = dataGridViewCellStyle3;
            dataGridView3.Size = new Size(1455, 700);
            dataGridView3.TabIndex = 0;
            // 
            // 查看指定第一个
            // 
            查看指定第一个.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            查看指定第一个.Location = new Point(392, 11);
            查看指定第一个.Margin = new Padding(4);
            查看指定第一个.Name = "查看指定第一个";
            查看指定第一个.Size = new Size(108, 33);
            查看指定第一个.TabIndex = 4;
            查看指定第一个.Text = "搜 索";
            查看指定第一个.UseVisualStyleBackColor = true;
            查看指定第一个.Click += 查看指定第一个_Click;
            // 
            // tabPage2
            // 
            tabPage2.Controls.Add(button13);
            tabPage2.Controls.Add(textBox1);
            tabPage2.Controls.Add(label18);
            tabPage2.Controls.Add(删除指定第一个);
            tabPage2.Controls.Add(查看全部);
            tabPage2.Controls.Add(dataGridView2);
            tabPage2.Location = new Point(4, 29);
            tabPage2.Name = "tabPage2";
            tabPage2.Padding = new Padding(3);
            tabPage2.Size = new Size(1467, 818);
            tabPage2.TabIndex = 1;
            tabPage2.Text = "产品信息";
            tabPage2.UseVisualStyleBackColor = true;
            // 
            // button13
            // 
            button13.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            button13.Location = new Point(404, 17);
            button13.Name = "button13";
            button13.Size = new Size(122, 55);
            button13.TabIndex = 19;
            button13.Text = "搜索型号";
            button13.UseVisualStyleBackColor = true;
            button13.Click += button13_Click;
            // 
            // textBox1
            // 
            textBox1.Font = new Font("Microsoft YaHei UI", 13.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            textBox1.Location = new Point(6, 24);
            textBox1.Name = "textBox1";
            textBox1.Size = new Size(392, 37);
            textBox1.TabIndex = 18;
            textBox1.GotFocus += textBox1_GotFocus;
            textBox1.KeyPress += TextBox1_KeyPress;
            textBox1.LostFocus += textBox1_LostFocus;
            // 
            // label18
            // 
            label18.AutoSize = true;
            label18.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Bold, GraphicsUnit.Point, 134);
            label18.ForeColor = Color.MediumSeaGreen;
            label18.Location = new Point(573, 30);
            label18.Name = "label18";
            label18.Size = new Size(60, 27);
            label18.TabIndex = 17;
            label18.Text = "SSSS";
            // 
            // 删除指定第一个
            // 
            删除指定第一个.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            删除指定第一个.Location = new Point(1308, 15);
            删除指定第一个.Margin = new Padding(4);
            删除指定第一个.Name = "删除指定第一个";
            删除指定第一个.Size = new Size(152, 57);
            删除指定第一个.TabIndex = 7;
            删除指定第一个.Text = "删除选中产品";
            删除指定第一个.UseVisualStyleBackColor = true;
            删除指定第一个.Click += 删除指定第一个_Click;
            // 
            // 查看全部
            // 
            查看全部.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            查看全部.Location = new Point(1055, 15);
            查看全部.Margin = new Padding(4);
            查看全部.Name = "查看全部";
            查看全部.Size = new Size(152, 57);
            查看全部.TabIndex = 16;
            查看全部.Text = "查看全部产品";
            查看全部.UseVisualStyleBackColor = true;
            查看全部.Click += 查看全部_Click;
            // 
            // dataGridView2
            // 
            dataGridView2.AllowUserToResizeColumns = false;
            dataGridView2.AllowUserToResizeRows = false;
            dataGridView2.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dataGridView2.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells;
            dataGridView2.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            dataGridViewCellStyle4.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle4.BackColor = SystemColors.Control;
            dataGridViewCellStyle4.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle4.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle4.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle4.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle4.WrapMode = DataGridViewTriState.True;
            dataGridView2.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle4;
            dataGridView2.ColumnHeadersHeight = 29;
            dataGridView2.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.DisableResizing;
            dataGridViewCellStyle5.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle5.BackColor = SystemColors.Window;
            dataGridViewCellStyle5.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle5.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle5.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle5.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle5.WrapMode = DataGridViewTriState.False;
            dataGridView2.DefaultCellStyle = dataGridViewCellStyle5;
            dataGridView2.Location = new Point(6, 79);
            dataGridView2.MultiSelect = false;
            dataGridView2.Name = "dataGridView2";
            dataGridViewCellStyle6.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle6.BackColor = SystemColors.Control;
            dataGridViewCellStyle6.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle6.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle6.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle6.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle6.WrapMode = DataGridViewTriState.True;
            dataGridView2.RowHeadersDefaultCellStyle = dataGridViewCellStyle6;
            dataGridView2.RowHeadersWidth = 51;
            dataGridViewCellStyle7.Alignment = DataGridViewContentAlignment.BottomLeft;
            dataGridViewCellStyle7.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            dataGridView2.RowsDefaultCellStyle = dataGridViewCellStyle7;
            dataGridView2.Size = new Size(1455, 733);
            dataGridView2.TabIndex = 0;
            dataGridView2.CellDoubleClick += dataGridView2_CellDoubleClick;
            // 
            // tabPage1
            // 
            tabPage1.Controls.Add(passnum);
            tabPage1.Controls.Add(label17);
            tabPage1.Controls.Add(label3);
            tabPage1.Controls.Add(groupBox2);
            tabPage1.Controls.Add(groupBox1);
            tabPage1.Controls.Add(dataGridView1);
            tabPage1.Controls.Add(label2_weight);
            tabPage1.Controls.Add(lbwarning2);
            tabPage1.Controls.Add(textsn);
            tabPage1.Controls.Add(label1);
            tabPage1.Controls.Add(lbpass);
            tabPage1.Location = new Point(4, 29);
            tabPage1.Name = "tabPage1";
            tabPage1.Padding = new Padding(3);
            tabPage1.Size = new Size(1467, 818);
            tabPage1.TabIndex = 0;
            tabPage1.Text = "称重";
            tabPage1.UseVisualStyleBackColor = true;
            // 
            // passnum
            // 
            passnum.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            passnum.AutoSize = true;
            passnum.Font = new Font("微软雅黑", 13.8F, FontStyle.Bold, GraphicsUnit.Point, 134);
            passnum.ForeColor = Color.MediumSeaGreen;
            passnum.Location = new Point(852, 141);
            passnum.Name = "passnum";
            passnum.Size = new Size(28, 31);
            passnum.TabIndex = 36;
            passnum.Text = "0";
            // 
            // label17
            // 
            label17.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label17.AutoSize = true;
            label17.Font = new Font("微软雅黑", 12F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label17.ForeColor = Color.Black;
            label17.Location = new Point(737, 143);
            label17.Name = "label17";
            label17.Size = new Size(112, 27);
            label17.TabIndex = 23;
            label17.Text = "完成数量：";
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label3.Location = new Point(17, 126);
            label3.Name = "label3";
            label3.Size = new Size(99, 27);
            label3.TabIndex = 14;
            label3.Text = "重量(g)：";
            // 
            // groupBox2
            // 
            groupBox2.Controls.Add(copyButton);
            groupBox2.Controls.Add(labeldate);
            groupBox2.Controls.Add(minvalue);
            groupBox2.Controls.Add(maxvalue);
            groupBox2.Controls.Add(averagevalue);
            groupBox2.Controls.Add(label4);
            groupBox2.Controls.Add(label6);
            groupBox2.Controls.Add(更新);
            groupBox2.Controls.Add(label5);
            groupBox2.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            groupBox2.Location = new Point(1001, 19);
            groupBox2.Name = "groupBox2";
            groupBox2.Size = new Size(460, 160);
            groupBox2.TabIndex = 22;
            groupBox2.TabStop = false;
            groupBox2.Text = "重量统计";
            // 
            // copyButton
            // 
            copyButton.Location = new Point(324, 26);
            copyButton.Name = "copyButton";
            copyButton.Size = new Size(116, 43);
            copyButton.TabIndex = 42;
            copyButton.Text = "备份数据";
            copyButton.UseVisualStyleBackColor = true;
            copyButton.Click += copyButton_Click;
            // 
            // labeldate
            // 
            labeldate.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            labeldate.AutoSize = true;
            labeldate.Font = new Font("微软雅黑", 9F, FontStyle.Regular, GraphicsUnit.Point, 134);
            labeldate.ForeColor = Color.Black;
            labeldate.Location = new Point(14, 131);
            labeldate.Name = "labeldate";
            labeldate.Size = new Size(99, 20);
            labeldate.TabIndex = 41;
            labeldate.Text = "解锁后更新：";
            // 
            // minvalue
            // 
            minvalue.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            minvalue.AutoSize = true;
            minvalue.Font = new Font("微软雅黑", 10.8F, FontStyle.Bold, GraphicsUnit.Point, 134);
            minvalue.ForeColor = Color.Black;
            minvalue.Location = new Point(143, 97);
            minvalue.Name = "minvalue";
            minvalue.Size = new Size(50, 25);
            minvalue.TabIndex = 40;
            minvalue.Text = "0.00";
            // 
            // maxvalue
            // 
            maxvalue.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            maxvalue.AutoSize = true;
            maxvalue.Font = new Font("微软雅黑", 10.8F, FontStyle.Bold, GraphicsUnit.Point, 134);
            maxvalue.ForeColor = Color.Black;
            maxvalue.Location = new Point(143, 65);
            maxvalue.Name = "maxvalue";
            maxvalue.Size = new Size(50, 25);
            maxvalue.TabIndex = 39;
            maxvalue.Text = "0.00";
            // 
            // averagevalue
            // 
            averagevalue.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            averagevalue.AutoSize = true;
            averagevalue.Font = new Font("微软雅黑", 10.8F, FontStyle.Bold, GraphicsUnit.Point, 134);
            averagevalue.ForeColor = Color.Black;
            averagevalue.Location = new Point(143, 33);
            averagevalue.Name = "averagevalue";
            averagevalue.Size = new Size(50, 25);
            averagevalue.TabIndex = 38;
            averagevalue.Text = "0.00";
            // 
            // label4
            // 
            label4.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label4.AutoSize = true;
            label4.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label4.ForeColor = Color.Black;
            label4.Location = new Point(13, 97);
            label4.Name = "label4";
            label4.Size = new Size(124, 24);
            label4.TabIndex = 37;
            label4.Text = "最小重量(g)：";
            // 
            // label6
            // 
            label6.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label6.AutoSize = true;
            label6.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label6.ForeColor = Color.Black;
            label6.Location = new Point(13, 65);
            label6.Name = "label6";
            label6.Size = new Size(124, 24);
            label6.TabIndex = 36;
            label6.Text = "最大重量(g)：";
            // 
            // 更新
            // 
            更新.Location = new Point(324, 82);
            更新.Margin = new Padding(4);
            更新.Name = "更新";
            更新.Size = new Size(116, 43);
            更新.TabIndex = 5;
            更新.Text = "更新重量";
            更新.UseVisualStyleBackColor = true;
            更新.Click += 更新_Click;
            // 
            // label5
            // 
            label5.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label5.AutoSize = true;
            label5.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label5.ForeColor = Color.Black;
            label5.Location = new Point(13, 33);
            label5.Name = "label5";
            label5.Size = new Size(124, 24);
            label5.TabIndex = 38;
            label5.Text = "平均重量(g)：";
            // 
            // groupBox1
            // 
            groupBox1.Controls.Add(button3);
            groupBox1.Controls.Add(插入固定);
            groupBox1.Controls.Add(SCtextBox);
            groupBox1.Controls.Add(snnumBox);
            groupBox1.Controls.Add(label2);
            groupBox1.Controls.Add(button4);
            groupBox1.Controls.Add(label15);
            groupBox1.Controls.Add(snBox);
            groupBox1.Controls.Add(button1);
            groupBox1.Controls.Add(clearbox);
            groupBox1.Controls.Add(label14);
            groupBox1.Controls.Add(JGtextBox);
            groupBox1.Controls.Add(minweightBox);
            groupBox1.Controls.Add(maxweightBox);
            groupBox1.Controls.Add(weightBox);
            groupBox1.Controls.Add(modelBox);
            groupBox1.Controls.Add(ordernumBox);
            groupBox1.Controls.Add(productcodeBox);
            groupBox1.Controls.Add(label13);
            groupBox1.Controls.Add(label12);
            groupBox1.Controls.Add(label11);
            groupBox1.Controls.Add(label10);
            groupBox1.Controls.Add(查看指定);
            groupBox1.Controls.Add(label9);
            groupBox1.Controls.Add(label8);
            groupBox1.Controls.Add(label7);
            groupBox1.Controls.Add(COMComboBox);
            groupBox1.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            groupBox1.Location = new Point(1001, 193);
            groupBox1.Name = "groupBox1";
            groupBox1.Size = new Size(460, 619);
            groupBox1.TabIndex = 21;
            groupBox1.TabStop = false;
            groupBox1.Text = "称重设置";
            // 
            // button3
            // 
            button3.BackColor = Color.Transparent;
            button3.Font = new Font("微软雅黑", 10.8F, FontStyle.Bold, GraphicsUnit.Point, 134);
            button3.ForeColor = Color.MediumPurple;
            button3.Location = new Point(324, 505);
            button3.Name = "button3";
            button3.Size = new Size(129, 108);
            button3.TabIndex = 37;
            button3.Text = "开 始 称 重";
            button3.UseVisualStyleBackColor = false;
            button3.Click += button3_Click;
            // 
            // 插入固定
            // 
            插入固定.Location = new Point(16, 505);
            插入固定.Margin = new Padding(4);
            插入固定.Name = "插入固定";
            插入固定.Size = new Size(121, 61);
            插入固定.TabIndex = 9;
            插入固定.Text = "添加新品";
            插入固定.UseVisualStyleBackColor = true;
            插入固定.Click += 插入固定_Click;
            // 
            // SCtextBox
            // 
            SCtextBox.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            SCtextBox.ForeColor = Color.Black;
            SCtextBox.Location = new Point(88, 142);
            SCtextBox.Name = "SCtextBox";
            SCtextBox.Size = new Size(365, 31);
            SCtextBox.TabIndex = 44;
            // 
            // snnumBox
            // 
            snnumBox.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            snnumBox.ForeColor = Color.Black;
            snnumBox.Location = new Point(109, 395);
            snnumBox.Name = "snnumBox";
            snnumBox.Size = new Size(106, 30);
            snnumBox.TabIndex = 35;
            snnumBox.TextAlign = HorizontalAlignment.Center;
            // 
            // label2
            // 
            label2.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label2.AutoSize = true;
            label2.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label2.ForeColor = Color.Black;
            label2.Location = new Point(26, 398);
            label2.Name = "label2";
            label2.Size = new Size(75, 24);
            label2.TabIndex = 34;
            label2.Text = "SN长度:";
            // 
            // button4
            // 
            button4.Location = new Point(168, 505);
            button4.Name = "button4";
            button4.Size = new Size(121, 61);
            button4.TabIndex = 38;
            button4.Text = "保存修改";
            button4.UseVisualStyleBackColor = true;
            button4.Click += button4_Click;
            // 
            // label15
            // 
            label15.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label15.AutoSize = true;
            label15.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label15.ForeColor = Color.Black;
            label15.Location = new Point(14, 145);
            label15.Name = "label15";
            label15.Size = new Size(68, 24);
            label15.TabIndex = 43;
            label15.Text = "操作员:";
            // 
            // snBox
            // 
            snBox.Font = new Font("Microsoft YaHei UI", 12F, FontStyle.Regular, GraphicsUnit.Point, 134);
            snBox.ForeColor = Color.Black;
            snBox.Location = new Point(15, 440);
            snBox.Name = "snBox";
            snBox.Size = new Size(438, 33);
            snBox.TabIndex = 33;
            snBox.GotFocus += snBox_GotFocus;
            snBox.KeyPress += snBox_KeyPress;
            snBox.LostFocus += snBox_LostFocus;
            // 
            // button1
            // 
            button1.BackColor = Color.Transparent;
            button1.Font = new Font("微软雅黑", 10.8F, FontStyle.Bold, GraphicsUnit.Point, 134);
            button1.ForeColor = Color.MediumPurple;
            button1.Location = new Point(327, 29);
            button1.Name = "button1";
            button1.Size = new Size(126, 52);
            button1.TabIndex = 32;
            button1.Text = "打开串口";
            button1.UseVisualStyleBackColor = false;
            button1.Click += button1_Click;
            // 
            // clearbox
            // 
            clearbox.ForeColor = Color.Red;
            clearbox.Location = new Point(108, 273);
            clearbox.Name = "clearbox";
            clearbox.Size = new Size(91, 33);
            clearbox.TabIndex = 39;
            clearbox.Text = "清 空";
            clearbox.UseVisualStyleBackColor = true;
            clearbox.Click += clearbox_Click;
            // 
            // label14
            // 
            label14.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label14.AutoSize = true;
            label14.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label14.ForeColor = Color.Black;
            label14.Location = new Point(13, 73);
            label14.Name = "label14";
            label14.Size = new Size(156, 24);
            label14.TabIndex = 41;
            label14.Text = "加工单/客户/备注:";
            // 
            // minweightBox
            // 
            minweightBox.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            minweightBox.ForeColor = Color.Black;
            minweightBox.Location = new Point(347, 395);
            minweightBox.Name = "minweightBox";
            minweightBox.Size = new Size(106, 30);
            minweightBox.TabIndex = 31;
            minweightBox.TextAlign = HorizontalAlignment.Center;
            // 
            // maxweightBox
            // 
            maxweightBox.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            maxweightBox.ForeColor = Color.Black;
            maxweightBox.Location = new Point(347, 354);
            maxweightBox.Name = "maxweightBox";
            maxweightBox.Size = new Size(106, 30);
            maxweightBox.TabIndex = 30;
            maxweightBox.TextAlign = HorizontalAlignment.Center;
            // 
            // weightBox
            // 
            weightBox.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            weightBox.ForeColor = Color.Black;
            weightBox.Location = new Point(107, 354);
            weightBox.Name = "weightBox";
            weightBox.Size = new Size(106, 30);
            weightBox.TabIndex = 29;
            weightBox.TextAlign = HorizontalAlignment.Center;
            // 
            // modelBox
            // 
            modelBox.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Bold, GraphicsUnit.Point, 134);
            modelBox.ForeColor = Color.Black;
            modelBox.Location = new Point(15, 312);
            modelBox.Name = "modelBox";
            modelBox.Size = new Size(439, 30);
            modelBox.TabIndex = 28;
            // 
            // ordernumBox
            // 
            ordernumBox.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            ordernumBox.ForeColor = Color.Black;
            ordernumBox.Location = new Point(109, 229);
            ordernumBox.Name = "ordernumBox";
            ordernumBox.Size = new Size(211, 30);
            ordernumBox.TabIndex = 27;
            // 
            // productcodeBox
            // 
            productcodeBox.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Bold, GraphicsUnit.Point, 134);
            productcodeBox.ForeColor = Color.Black;
            productcodeBox.Location = new Point(108, 186);
            productcodeBox.Name = "productcodeBox";
            productcodeBox.Size = new Size(212, 30);
            productcodeBox.TabIndex = 1;
            // 
            // label13
            // 
            label13.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label13.AutoSize = true;
            label13.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label13.ForeColor = Color.Black;
            label13.Location = new Point(246, 398);
            label13.Name = "label13";
            label13.Size = new Size(86, 24);
            label13.TabIndex = 26;
            label13.Text = "重量下限:";
            // 
            // label12
            // 
            label12.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label12.AutoSize = true;
            label12.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label12.ForeColor = Color.Black;
            label12.Location = new Point(246, 357);
            label12.Name = "label12";
            label12.Size = new Size(86, 24);
            label12.TabIndex = 25;
            label12.Text = "重量上限:";
            // 
            // label11
            // 
            label11.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label11.AutoSize = true;
            label11.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label11.ForeColor = Color.Black;
            label11.Location = new Point(15, 357);
            label11.Name = "label11";
            label11.Size = new Size(86, 24);
            label11.TabIndex = 24;
            label11.Text = "标准重量:";
            // 
            // label10
            // 
            label10.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label10.AutoSize = true;
            label10.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label10.ForeColor = Color.Black;
            label10.Location = new Point(13, 284);
            label10.Name = "label10";
            label10.Size = new Size(86, 24);
            label10.TabIndex = 23;
            label10.Text = "产品型号:";
            // 
            // 查看指定
            // 
            查看指定.Location = new Point(327, 186);
            查看指定.Margin = new Padding(4);
            查看指定.Name = "查看指定";
            查看指定.Size = new Size(126, 76);
            查看指定.TabIndex = 3;
            查看指定.Text = "搜索编码";
            查看指定.UseVisualStyleBackColor = true;
            查看指定.Click += 查看指定_Click;
            // 
            // label9
            // 
            label9.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label9.AutoSize = true;
            label9.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label9.ForeColor = Color.Black;
            label9.Location = new Point(13, 234);
            label9.Name = "label9";
            label9.Size = new Size(86, 24);
            label9.TabIndex = 22;
            label9.Text = "订货号码:";
            // 
            // label8
            // 
            label8.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label8.AutoSize = true;
            label8.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label8.ForeColor = Color.Black;
            label8.Location = new Point(13, 189);
            label8.Name = "label8";
            label8.Size = new Size(86, 24);
            label8.TabIndex = 21;
            label8.Text = "产品编码:";
            // 
            // label7
            // 
            label7.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label7.AutoSize = true;
            label7.Font = new Font("微软雅黑", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label7.ForeColor = Color.Black;
            label7.Location = new Point(65, 42);
            label7.Name = "label7";
            label7.Size = new Size(104, 24);
            label7.TabIndex = 19;
            label7.Text = "电子秤串口:";
            // 
            // COMComboBox
            // 
            COMComboBox.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            COMComboBox.FormattingEnabled = true;
            COMComboBox.Location = new Point(191, 39);
            COMComboBox.Name = "COMComboBox";
            COMComboBox.Size = new Size(126, 32);
            COMComboBox.TabIndex = 20;
            // 
            // dataGridView1
            // 
            dataGridView1.AllowUserToDeleteRows = false;
            dataGridView1.AllowUserToResizeColumns = false;
            dataGridView1.AllowUserToResizeRows = false;
            dataGridViewCellStyle8.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridView1.AlternatingRowsDefaultCellStyle = dataGridViewCellStyle8;
            dataGridView1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            dataGridView1.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.AllCells;
            dataGridView1.AutoSizeRowsMode = DataGridViewAutoSizeRowsMode.AllCells;
            dataGridView1.CausesValidation = false;
            dataGridViewCellStyle9.BackColor = SystemColors.Control;
            dataGridViewCellStyle9.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle9.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle9.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle9.SelectionForeColor = SystemColors.HighlightText;
            dataGridView1.ColumnHeadersDefaultCellStyle = dataGridViewCellStyle9;
            dataGridView1.ColumnHeadersHeightSizeMode = DataGridViewColumnHeadersHeightSizeMode.AutoSize;
            dataGridView1.Columns.AddRange(new DataGridViewColumn[] { Column1, Column2, Column3, Column4, Column5, Column6, Column7 });
            dataGridViewCellStyle11.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle11.BackColor = SystemColors.Window;
            dataGridViewCellStyle11.Font = new Font("Microsoft YaHei UI", 9F);
            dataGridViewCellStyle11.ForeColor = SystemColors.ControlText;
            dataGridViewCellStyle11.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle11.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle11.WrapMode = DataGridViewTriState.False;
            dataGridView1.DefaultCellStyle = dataGridViewCellStyle11;
            dataGridView1.Location = new Point(6, 193);
            dataGridView1.MultiSelect = false;
            dataGridView1.Name = "dataGridView1";
            dataGridView1.ReadOnly = true;
            dataGridViewCellStyle12.Alignment = DataGridViewContentAlignment.MiddleCenter;
            dataGridViewCellStyle12.BackColor = SystemColors.Control;
            dataGridViewCellStyle12.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            dataGridViewCellStyle12.ForeColor = SystemColors.WindowText;
            dataGridViewCellStyle12.SelectionBackColor = SystemColors.Highlight;
            dataGridViewCellStyle12.SelectionForeColor = SystemColors.HighlightText;
            dataGridViewCellStyle12.WrapMode = DataGridViewTriState.True;
            dataGridView1.RowHeadersDefaultCellStyle = dataGridViewCellStyle12;
            dataGridView1.RowHeadersWidth = 51;
            dataGridViewCellStyle13.Font = new Font("Microsoft YaHei UI", 10.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            dataGridView1.RowsDefaultCellStyle = dataGridViewCellStyle13;
            dataGridView1.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView1.ShowCellErrors = false;
            dataGridView1.ShowCellToolTips = false;
            dataGridView1.Size = new Size(989, 619);
            dataGridView1.TabIndex = 15;
            // 
            // Column1
            // 
            Column1.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            dataGridViewCellStyle10.Alignment = DataGridViewContentAlignment.MiddleCenter;
            Column1.DefaultCellStyle = dataGridViewCellStyle10;
            Column1.HeaderText = "序号";
            Column1.MinimumWidth = 6;
            Column1.Name = "Column1";
            Column1.ReadOnly = true;
            Column1.Width = 68;
            // 
            // Column2
            // 
            Column2.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            Column2.HeaderText = "产品型号";
            Column2.MinimumWidth = 6;
            Column2.Name = "Column2";
            Column2.ReadOnly = true;
            Column2.Width = 98;
            // 
            // Column3
            // 
            Column3.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            Column3.HeaderText = "SN号";
            Column3.MinimumWidth = 6;
            Column3.Name = "Column3";
            Column3.ReadOnly = true;
            Column3.Width = 74;
            // 
            // Column4
            // 
            Column4.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            Column4.HeaderText = "重量";
            Column4.MinimumWidth = 6;
            Column4.Name = "Column4";
            Column4.ReadOnly = true;
            Column4.Width = 68;
            // 
            // Column5
            // 
            Column5.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            Column5.HeaderText = "误差";
            Column5.MinimumWidth = 6;
            Column5.Name = "Column5";
            Column5.ReadOnly = true;
            Column5.Width = 68;
            // 
            // Column6
            // 
            Column6.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            Column6.HeaderText = "结果";
            Column6.MinimumWidth = 6;
            Column6.Name = "Column6";
            Column6.ReadOnly = true;
            Column6.Width = 68;
            // 
            // Column7
            // 
            Column7.AutoSizeMode = DataGridViewAutoSizeColumnMode.AllCells;
            Column7.HeaderText = "时间";
            Column7.MinimumWidth = 6;
            Column7.Name = "Column7";
            Column7.ReadOnly = true;
            Column7.Width = 68;
            // 
            // label2_weight
            // 
            label2_weight.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            label2_weight.AutoSize = true;
            label2_weight.BackColor = Color.Transparent;
            label2_weight.Font = new Font("DIN", 49.7999954F, FontStyle.Bold, GraphicsUnit.Point, 0);
            label2_weight.ForeColor = Color.MediumSeaGreen;
            label2_weight.ImageAlign = ContentAlignment.MiddleLeft;
            label2_weight.Location = new Point(57, 79);
            label2_weight.Name = "label2_weight";
            label2_weight.Size = new Size(0, 101);
            label2_weight.TabIndex = 13;
            label2_weight.TextAlign = ContentAlignment.MiddleLeft;
            // 
            // lbwarning2
            // 
            lbwarning2.Font = new Font("微软雅黑", 13.8F, FontStyle.Bold, GraphicsUnit.Point, 134);
            lbwarning2.ForeColor = Color.MediumPurple;
            lbwarning2.Location = new Point(613, 19);
            lbwarning2.Name = "lbwarning2";
            lbwarning2.Size = new Size(371, 52);
            lbwarning2.TabIndex = 34;
            lbwarning2.TextAlign = ContentAlignment.MiddleCenter;
            lbwarning2.Click += lbwarning2_Click;
            // 
            // textsn
            // 
            textsn.Font = new Font("MiSans", 18F, FontStyle.Regular, GraphicsUnit.Point, 134);
            textsn.ForeColor = Color.MediumPurple;
            textsn.Location = new Point(103, 21);
            textsn.Name = "textsn";
            textsn.Size = new Size(505, 47);
            textsn.TabIndex = 11;
            textsn.KeyPress += textsn_KeyPress;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new Font("Microsoft YaHei UI", 13.8F, FontStyle.Regular, GraphicsUnit.Point, 134);
            label1.Location = new Point(17, 30);
            label1.Name = "label1";
            label1.Size = new Size(91, 30);
            label1.TabIndex = 10;
            label1.Text = "SN号：";
            // 
            // lbpass
            // 
            lbpass.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            lbpass.AutoSize = true;
            lbpass.BackColor = Color.Transparent;
            lbpass.Font = new Font("微软雅黑", 49.8000031F, FontStyle.Bold, GraphicsUnit.Point, 134);
            lbpass.ForeColor = Color.MediumPurple;
            lbpass.Location = new Point(448, 74);
            lbpass.Name = "lbpass";
            lbpass.Size = new Size(215, 112);
            lbpass.TabIndex = 18;
            lbpass.Text = "等待";
            // 
            // button2
            // 
            button2.Location = new Point(207, 33);
            button2.Name = "button2";
            button2.Size = new Size(99, 38);
            button2.TabIndex = 33;
            button2.Text = "获取重量";
            button2.UseVisualStyleBackColor = true;
            button2.Click += button2_Click;
            // 
            // 删除指定
            // 
            删除指定.Location = new Point(26, 460);
            删除指定.Margin = new Padding(4);
            删除指定.Name = "删除指定";
            删除指定.Size = new Size(99, 43);
            删除指定.TabIndex = 6;
            删除指定.Text = "删除指定";
            删除指定.UseVisualStyleBackColor = true;
            删除指定.Click += 删除指定_Click;
            // 
            // Commit
            // 
            Commit.Location = new Point(206, 393);
            Commit.Margin = new Padding(4);
            Commit.Name = "Commit";
            Commit.Size = new Size(96, 27);
            Commit.TabIndex = 8;
            Commit.Text = "Commit";
            Commit.UseVisualStyleBackColor = true;
            Commit.Click += Commit_Click;
            // 
            // 插入
            // 
            插入.Location = new Point(219, 441);
            插入.Margin = new Padding(4);
            插入.Name = "插入";
            插入.Size = new Size(83, 27);
            插入.TabIndex = 1;
            插入.Text = "插入随机";
            插入.UseVisualStyleBackColor = true;
            插入.Click += 插入_Click;
            // 
            // 删除全部
            // 
            删除全部.Location = new Point(227, 476);
            删除全部.Margin = new Padding(4);
            删除全部.Name = "删除全部";
            删除全部.Size = new Size(89, 27);
            删除全部.TabIndex = 2;
            删除全部.Text = "删除全部";
            删除全部.UseVisualStyleBackColor = true;
            删除全部.Click += 删除全部_Click;
            // 
            // tabControl1
            // 
            tabControl1.Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right;
            tabControl1.Controls.Add(tabPage1);
            tabControl1.Controls.Add(tabPage2);
            tabControl1.Controls.Add(tabPage3);
            tabControl1.Location = new Point(12, 12);
            tabControl1.Name = "tabControl1";
            tabControl1.SelectedIndex = 0;
            tabControl1.Size = new Size(1475, 851);
            tabControl1.TabIndex = 15;
            tabControl1.SelectedIndexChanged += tabControl1_SelectedIndexChanged;
            // 
            // Form1
            // 
            AutoScaleDimensions = new SizeF(9F, 20F);
            AutoScaleMode = AutoScaleMode.Font;
            ClientSize = new Size(1499, 875);
            Controls.Add(tabControl1);
            FormBorderStyle = FormBorderStyle.FixedSingle;
            Margin = new Padding(4);
            MaximizeBox = false;
            Name = "Form1";
            StartPosition = FormStartPosition.CenterScreen;
            Text = "WeightCheck  V1.3";
            FormClosing += Form1_FormClosing;
            FormClosed += Form1_FormClosed;
            Load += Form1_Load;
            ((System.ComponentModel.ISupportInitialize)productItemBindingSource).EndInit();
            tabPage3.ResumeLayout(false);
            tabPage3.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView3).EndInit();
            tabPage2.ResumeLayout(false);
            tabPage2.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView2).EndInit();
            tabPage1.ResumeLayout(false);
            tabPage1.PerformLayout();
            groupBox2.ResumeLayout(false);
            groupBox2.PerformLayout();
            groupBox1.ResumeLayout(false);
            groupBox1.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)dataGridView1).EndInit();
            tabControl1.ResumeLayout(false);
            ResumeLayout(false);
        }





        #endregion
        private ListBox listBox1;
        private ListBox listBox2;
        private BindingSource productItemBindingSource;
        private TabPage tabPage3;
        private DataGridView dataGridView3;
        private TabPage tabPage2;
        private Button 查看全部;
        private DataGridView dataGridView2;
        private TabPage tabPage1;
        private Button 查看指定第一个;
        private Label passnum;
        private Label label17;
        private Label label3;
        private GroupBox groupBox2;
        private Label labeldate;
        private Label minvalue;
        private Label maxvalue;
        private Label averagevalue;
        private Label label4;
        private Button button2;
        private Label label6;
        private Button 更新;
        private Label label5;
        private GroupBox groupBox1;
        private Button button3;
        private TextBox snnumBox;
        private Label label2;
        private TextBox snBox;
        private Button button1;
        private TextBox minweightBox;
        private Button Commit;
        private TextBox maxweightBox;
        private TextBox weightBox;
        private TextBox modelBox;
        private Button 插入固定;
        private Button 插入;
        private TextBox ordernumBox;
        private TextBox productcodeBox;
        private Button 删除指定第一个;
        private Label label13;
        private Label label12;
        private Button 删除全部;
        private Button 删除指定;
        private Label label11;
        private Label label10;
        private Button 查看指定;
        private Label label9;
        private Label label8;
        private Label label7;
        private ComboBox COMComboBox;
        private DataGridView dataGridView1;
        private Label label2_weight;
        private Label lbwarning2;
        private TextBox textsn;
        private Label label1;
        private Label lbpass;
        private TabControl tabControl1;
        private Button button4;
        private TextBox searchBox;
        private Button clearbox;
        private Label label15;
        private Label label14;
        private TextBox SCtextBox;
        private Button button6;
        private RadioButton coderadioButton;
        private RadioButton scradioButton;
        private RadioButton jgradioButton;
        private RadioButton snradioButton;
        private Button button7;
        private Button button5;
        private Label label18;
        private Button button8;
        private RadioButton ModelradioButton;
        private Label labRecordCount;
        private Label labPageIndex;
        private Button button10;
        private Button button9;
        private Button button12;
        private Button button11;
        private Button button13;
        private TextBox textBox1;
        private TextBox JGtextBox;
        private Button copyButton;
        private DataGridViewTextBoxColumn Column1;
        private DataGridViewTextBoxColumn Column2;
        private DataGridViewTextBoxColumn Column3;
        private DataGridViewTextBoxColumn Column4;
        private DataGridViewTextBoxColumn Column5;
        private DataGridViewTextBoxColumn Column6;
        private DataGridViewTextBoxColumn Column7;
        private Button button14;
    }
}
