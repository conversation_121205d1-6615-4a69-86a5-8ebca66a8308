﻿using LiteDB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Weightdb
{
    /// <summary>
    /// 表示 TranslItem
    /// </summary>
   

    public class WeightItem
    {
        //主键
        [BsonId]
        public int Id{ get; set; }//序号，自动生成
                                   //文本
        public string? Productcode { get; set; }//产品编码productcode
        
        public string? Model { get; set; }//产品型号
        public string? Productsn { get; set; }//产品序列号
        public double Weight { get; set; }//实测重量
        public double? Weighterr { get; set; }//重量误差
        public string? Pass { get; set; }//是否合格
        public string? Jgno { get; set; }//加工单号
        public string? Scperson { get; set; }//操作人
        public string? Weightdate { get; set; }//称重时间
        //保存称重记录的函数，保存到表格，保存到新表
    }

}
