﻿using LiteDB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Weightdb;

namespace LiteTest.Lite//命名空间WeightCheck.Lite
{
  /// <summary>
  /// 表示 LiteContext
  /// </summary>
  public class LiteContext 
  {

    static LiteContext()
    {
      lite = new("E:\\mycode\\LiteTest\\LiteTest\\LiteTest\\bin\\Debug\\net8.0-windows\\productdata.db");//数据库
      products = lite.GetCollection<ProductItem>("products");//启动创建产品信息表
    
        }

        readonly static LiteDatabase lite;
        readonly static ILiteCollection<ProductItem> products;
       
        public static LiteDatabase Lite => lite;//声明数据库的全局变量，简称：weight，product
       public static ILiteCollection<ProductItem> TranslList => products;//声明表的全局变量，weights，products

       ILiteCollection<ProductItem> GetLiteCollection()//创建库
    {
      // 如果文件不存在，它会被创建
    var db = new LiteDatabase("productdata.db");
     var ts = db.GetCollection<ProductItem>("products");
     return ts;
    }
       


    }
}
