﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
    <configSections>
        <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" >
            <section name="LiteTest.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
        </sectionGroup>
    </configSections>
    <userSettings>
        <LiteTest.Properties.Settings>
            <setting name="jgdh" serializeAs="String">
                <value />
            </setting>
            <setting name="czr" serializeAs="String">
                <value />
            </setting>
        </LiteTest.Properties.Settings>
    </userSettings>
</configuration>