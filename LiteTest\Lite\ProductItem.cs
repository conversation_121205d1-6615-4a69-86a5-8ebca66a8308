﻿using LiteDB;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace LiteTest.Lite
{
  /// <summary>
  /// 表示 TranslItem
  /// </summary>
  public class ProductItem
  {
    //主键
    [BsonId]
    public int Id { get; set; }//序号，自动生成
    //文本
    public string? Productcode { get; set; }//产品编码productcode

   public string? Ordernum { get; set; }//订货号
   public string? Model { get; set; }//产品型号
   public double? Weight { get; set; }//标准重量
   public double? Maxweight{ get; set; }//重量上限
   public double? Minweight { get; set; }//重量下限minweight
   public int Snnum { get; set; }//条码长度
        public string? Sndate { get; set; }//创建/更新时间
    }

   

}
