D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.exe
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.deps.json
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.runtimeconfig.json
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.dll
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.pdb
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteDB.dll
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.AssemblyReference.cache
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.Form1.resources
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.GenerateResource.cache
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.GeneratedMSBuildEditorConfig.editorconfig
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.AssemblyInfoInputs.cache
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.AssemblyInfo.cs
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.CoreCompileInputs.cache
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.Up2Date
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.dll
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\obj\Debug\net8.0-windows\refint\LiteTest.dll
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.pdb
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.genruntimeconfig.cache
D:\VSProject\Csharp\WinForm\LiteTest\LiteTest\obj\Debug\net8.0-windows\ref\LiteTest.dll
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.exe
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.deps.json
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.runtimeconfig.json
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.dll
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.pdb
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteDB.dll
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\System.IO.Ports.dll
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libSystem.IO.Ports.Native.so
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libSystem.IO.Ports.Native.so
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libSystem.IO.Ports.Native.so
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\osx-arm64\native\libSystem.IO.Ports.Native.dylib
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\osx-x64\native\libSystem.IO.Ports.Native.dylib
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\unix\lib\net8.0\System.IO.Ports.dll
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.IO.Ports.dll
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.AssemblyReference.cache
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.Form1.resources
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.GenerateResource.cache
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.GeneratedMSBuildEditorConfig.editorconfig
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.AssemblyInfoInputs.cache
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.AssemblyInfo.cs
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.CoreCompileInputs.cache
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.Up2Date
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.dll
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\refint\LiteTest.dll
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.pdb
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.genruntimeconfig.cache
E:\BaiduNetdiskDownload\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\ref\LiteTest.dll
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.exe
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.deps.json
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.runtimeconfig.json
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.dll
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.pdb
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteDB.dll
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\System.IO.Ports.dll
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libSystem.IO.Ports.Native.so
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libSystem.IO.Ports.Native.so
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libSystem.IO.Ports.Native.so
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\osx-arm64\native\libSystem.IO.Ports.Native.dylib
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\osx-x64\native\libSystem.IO.Ports.Native.dylib
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\unix\lib\net8.0\System.IO.Ports.dll
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.IO.Ports.dll
E:\mycode\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.AssemblyReference.cache
E:\mycode\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.Form1.resources
E:\mycode\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.GenerateResource.cache
E:\mycode\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.GeneratedMSBuildEditorConfig.editorconfig
E:\mycode\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.AssemblyInfoInputs.cache
E:\mycode\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.AssemblyInfo.cs
E:\mycode\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.CoreCompileInputs.cache
E:\mycode\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.Up2Date
E:\mycode\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.dll
E:\mycode\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\refint\LiteTest.dll
E:\mycode\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.pdb
E:\mycode\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.genruntimeconfig.cache
E:\mycode\LiteTest\LiteTest\LiteTest\obj\Debug\net8.0-windows\ref\LiteTest.dll
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\LiteTest.exe
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\LiteTest.deps.json
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\LiteTest.runtimeconfig.json
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\LiteTest.dll
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\LiteTest.pdb
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\LiteDB.dll
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\System.IO.Ports.dll
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\runtimes\linux-arm\native\libSystem.IO.Ports.Native.so
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\runtimes\linux-arm64\native\libSystem.IO.Ports.Native.so
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\runtimes\linux-x64\native\libSystem.IO.Ports.Native.so
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\runtimes\osx-arm64\native\libSystem.IO.Ports.Native.dylib
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\runtimes\osx-x64\native\libSystem.IO.Ports.Native.dylib
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\runtimes\unix\lib\net8.0\System.IO.Ports.dll
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.IO.Ports.dll
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\net8.0-windows\Debug\net8.0-windows\Interop.SpeechLib.dll
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\Debug\net8.0-windows\LiteTest.exe
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\Debug\net8.0-windows\LiteTest.deps.json
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\Debug\net8.0-windows\LiteTest.runtimeconfig.json
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\Debug\net8.0-windows\LiteTest.dll
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\Debug\net8.0-windows\LiteTest.pdb
E:\mycode\LiteTest\LiteTest\LiteTest\bin\Debug\Debug\net8.0-windows\LiteTest.dll.config
I:\重量\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.AssemblyReference.cache
I:\重量\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.Form1.resources
I:\重量\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.GenerateResource.cache
I:\重量\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.GeneratedMSBuildEditorConfig.editorconfig
I:\重量\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.AssemblyInfoInputs.cache
I:\重量\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.AssemblyInfo.cs
I:\重量\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.CoreCompileInputs.cache
I:\重量\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.csproj.Up2Date
I:\重量\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.dll
I:\重量\LiteTest\LiteTest\obj\Debug\net8.0-windows\refint\LiteTest.dll
I:\重量\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.pdb
I:\重量\LiteTest\LiteTest\obj\Debug\net8.0-windows\LiteTest.genruntimeconfig.cache
I:\重量\LiteTest\LiteTest\obj\Debug\net8.0-windows\ref\LiteTest.dll
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.exe
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.dll.config
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.deps.json
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.runtimeconfig.json
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.dll
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteTest.pdb
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\LiteDB.dll
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\System.IO.Ports.dll
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\linux-arm\native\libSystem.IO.Ports.Native.so
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\linux-arm64\native\libSystem.IO.Ports.Native.so
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\linux-x64\native\libSystem.IO.Ports.Native.so
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\osx-arm64\native\libSystem.IO.Ports.Native.dylib
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\osx-x64\native\libSystem.IO.Ports.Native.dylib
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\unix\lib\net8.0\System.IO.Ports.dll
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\runtimes\win\lib\net8.0\System.IO.Ports.dll
I:\重量\LiteTest\LiteTest\bin\Debug\net8.0-windows\Interop.SpeechLib.dll
