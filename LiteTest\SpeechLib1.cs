﻿using SpeechLib;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Media;
using System.Text;
using System.Threading.Tasks;


namespace SpeechLib1
{
    public class CVoice
    {
        private static Speach mysp = Speach.instance();
        private static SoundPlayer _voiceplayer = new SoundPlayer();
        private static Thread threadplay;
        private static string _sound;
        public static string SoundfileName
        {
            get { return _sound; }
        }

        private static string _soundtext;
        public static string SoundText
        {
            get { return CVoice._soundtext; }
        }

        public static void Playfile(string soundfile)
        {
            _sound = soundfile;
            threadplay = new Thread(mysoundplay);
            threadplay.Start();
        }

        public static void Play(string soundtext)
        {
            try
            {
                _soundtext = soundtext.Replace("重", "虫");  //解决都“zhong”的问题
                mysp.AnalyseSpeak(soundtext);
            }
            catch
            {
            }
        }

        public static void RePlay() //播放最后一次的语音
        {
            mysp.AnalyseSpeak(_soundtext);
        }

        public static void Pause() //播放最后一次的语音
        {
            mysp.Pause();
        }

        public static void Continue() //播放最后一次的语音
        {
            mysp.Continue();
        }

        public static void Stop()
        {
            //if (threadplay.ThreadState == ThreadState.Running)
            //{
            //    threadplay.Abort();
            //}
            mysp.Stop();
        }

        private static void mysoundplay()
        {
            try
            {
                _voiceplayer.SoundLocation = _sound;
                _voiceplayer.Play();
            }
            catch
            {
            }
        }

        public static Speach Speech
        {
            get
            {
                return mysp;
            }
        }
    }

    public class Speach
    {
        private static Speach _Instance = null;
        private SpeechLib.SpVoiceClass voice = null;

        private Speach()
        {
            BuildSpeach();
        }
        public static Speach instance()
        {
            if (_Instance == null)
                _Instance = new Speach();
            return _Instance;
        }

        public void SelectVoice(int index)
        {
            try
            {
                voice.Voice = voice.GetVoices("", "").Item(index);
            }
            catch
            {
            }
        }

        private void BuildSpeach()
        {
            if (voice == null) voice = new SpVoiceClass();
            SelectVoice(0);
        }
        public int Volume
        {
            get
            {
                return voice.Volume;
            }
            set
            {
                voice.SetVolume((ushort)(value));
            }
        }
        public int Rate
        {
            get
            {
                return voice.Rate;
            }
            set
            {
                voice.SetRate(value);
            }
        }
        private void Speak(string strSpeack)
        {
            try
            {
                int nn = voice.Speak(strSpeack.Replace("重", "虫"), SpeechLib.SpeechVoiceSpeakFlags.SVSFlagsAsync);
                Console.WriteLine(nn);
            }
            catch (Exception err)
            {
                throw (new Exception("发生一个错误：" + err.Message));
            }
        }

        public void AnalyseSpeak(string strSpeak)
        {
            Speak(strSpeak);
        }

        public void Stop()
        {
            voice.Speak(string.Empty, SpeechLib.SpeechVoiceSpeakFlags.SVSFPurgeBeforeSpeak);
        }
        public void Pause()
        {
            voice.Pause();
        }
        public void Continue()
        {
            voice.Resume();
        }
    }
}
